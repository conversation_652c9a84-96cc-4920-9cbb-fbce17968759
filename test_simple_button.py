"""
Test the simplified button approach
"""
import tkinter as tk

def test_simple_button():
    """Test a simple, visible button."""
    print("🧪 Testing Simplified <PERSON><PERSON>")
    print("=" * 30)
    
    # Create test window
    root = tk.Tk()
    root.title("Simple Button Test")
    root.geometry("600x400")
    root.configure(bg='#F5F5F5')
    
    # Main container
    main_frame = tk.Frame(root, bg='#F5F5F5')
    main_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # Title
    tk.Label(
        main_frame,
        text="Button Visibility Test",
        font=('Segoe UI', 16, 'bold'),
        bg='#F5F5F5',
        fg='#333333'
    ).pack(pady=20)
    
    # Test the exact button configuration from the main app
    button_frame = tk.Frame(main_frame, bg='#F5F5F5')
    button_frame.pack(pady=30)
    
    def button_clicked():
        tk.messagebox.showinfo("Test", "Button is working!")
        print("✅ Button clicked successfully!")
    
    # Test button with exact same config as main app
    test_btn = tk.Button(
        button_frame,
        text="📄 UPLOAD & ANALYZE ROOF REPORT",
        command=button_clicked,
        font=('Segoe UI', 18, 'bold'),
        bg='#DDDDDD',  # Light gray but clearly visible
        fg='#333333',  # Dark text for good contrast
        relief='raised',
        borderwidth=3,
        cursor='hand2',
        padx=30,
        pady=20,
        width=35,
        height=3,
        state='normal'  # Enabled for testing
    )
    test_btn.pack(pady=10)
    
    print("✅ Created test button with:")
    print(f"  • Background: #DDDDDD (light gray)")
    print(f"  • Text: #333333 (dark gray)")
    print(f"  • Relief: raised")
    print(f"  • Border: 3px")
    print(f"  • Size: 35x3")
    print(f"  • State: normal")
    
    # Status
    tk.Label(
        main_frame,
        text="👆 If you can see the button above, the visibility is fixed!",
        font=('Segoe UI', 12),
        bg='#F5F5F5',
        fg='#666666'
    ).pack(pady=10)
    
    # Test disabled state
    tk.Label(
        main_frame,
        text="Testing Disabled State:",
        font=('Segoe UI', 12, 'bold'),
        bg='#F5F5F5',
        fg='#333333'
    ).pack(pady=(20, 5))
    
    disabled_btn = tk.Button(
        main_frame,
        text="📄 DISABLED BUTTON",
        font=('Segoe UI', 18, 'bold'),
        bg='#DDDDDD',
        fg='#333333',
        relief='raised',
        borderwidth=3,
        cursor='arrow',
        padx=30,
        pady=20,
        width=35,
        height=3,
        state='disabled'
    )
    disabled_btn.pack(pady=5)
    
    tk.Label(
        main_frame,
        text="👆 This shows how the button looks when disabled",
        font=('Segoe UI', 10),
        bg='#F5F5F5',
        fg='#666666'
    ).pack(pady=5)
    
    # Close button
    tk.Button(
        main_frame,
        text="Close Test",
        command=root.destroy,
        font=('Segoe UI', 10),
        bg='#FF6B6B',
        fg='white',
        padx=15,
        pady=5
    ).pack(pady=20)
    
    print(f"\n🖱️ Test window opened!")
    print(f"You should see:")
    print(f"  • A large gray button with dark text")
    print(f"  • The button should be clearly visible")
    print(f"  • Click it to test functionality")
    
    # Run test
    root.mainloop()
    
    return True

def main():
    """Run the simple button test."""
    print("🏠 Isaiah Industries - Simple Button Test")
    print("🔍 Testing if the simplified button approach works")
    
    try:
        test_simple_button()
        print(f"\n✅ Test completed!")
        print(f"If you saw the button clearly, the fix is working.")
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    print(f"\n💡 The simplified approach:")
    print("• Removed complex shadow effects")
    print("• Used simple pack() instead of place()")
    print("• Higher contrast colors")
    print("• Larger, more visible button")
    print("• Raised relief for better visibility")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
