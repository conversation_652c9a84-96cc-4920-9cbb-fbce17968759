"""
Build script for creating Isaiah Industries Roof Material Tool executable
"""
import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])

def build_exe():
    """Build the executable using PyInstaller"""
    print("Building executable...")
    
    # PyInstaller command with options
    cmd = [
        "pyinstaller",
        "--onefile",  # Create a single executable file
        "--windowed",  # Hide console window (GUI app)
        "--name", "Isaiah_Industries_Roof_Tool",  # Name of the executable
        "--icon", "icon.ico" if os.path.exists("icon.ico") else None,  # Icon if available
        "--add-data", "parts.csv;.",  # Include the CSV file
        "--hidden-import", "pdfplumber",
        "--hidden-import", "pdf2image",
        "--hidden-import", "pytesseract",
        "--hidden-import", "PIL",
        "roof_material_tool.py"
    ]
    
    # Remove None values (icon if not present)
    cmd = [arg for arg in cmd if arg is not None]
    
    subprocess.check_call(cmd)
    print("Build complete! Check the 'dist' folder for your executable.")

if __name__ == "__main__":
    try:
        install_requirements()
        build_exe()
        print("\n✅ SUCCESS! Your executable is ready in the 'dist' folder.")
        print("📁 File: dist/Isaiah_Industries_Roof_Tool.exe")
        print("\n📋 To distribute:")
        print("1. Copy the .exe file to any Windows computer")
        print("2. Make sure Tesseract OCR is installed on target computers")
        print("3. The parts.csv file is embedded in the executable")
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have Python and pip installed.")
