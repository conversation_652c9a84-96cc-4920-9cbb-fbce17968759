"""
Test the no-dialog fix for PDF generation
"""
import sys
import os

def test_automatic_filename():
    """Test the automatic filename generation."""
    print("🧪 Testing Automatic Filename Generation")
    print("=" * 50)
    
    try:
        import datetime
        
        # Test filename generation logic
        product = "Country Manor Shake"
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_product_name = "".join(c for c in product if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_product_name = safe_product_name.replace(' ', '_')
        
        print(f"📋 Product: {product}")
        print(f"📋 Safe name: {safe_product_name}")
        print(f"📋 Timestamp: {timestamp}")
        
        # Test desktop path
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        print(f"📁 Desktop path: {desktop}")
        print(f"📁 Desktop exists: {os.path.exists(desktop)}")
        
        if os.path.exists(desktop):
            save_path = os.path.join(desktop, f"{safe_product_name}_Material_List_{timestamp}.pdf")
            save_location = "Desktop"
        else:
            save_path = f"{safe_product_name}_Material_List_{timestamp}.pdf"
            save_location = "current directory"
        
        print(f"📄 Generated filename: {os.path.basename(save_path)}")
        print(f"📄 Full path: {save_path}")
        print(f"📄 Save location: {save_location}")
        
        # Test that the path is valid
        try:
            # Test if we can create a file at this location
            test_content = b"test"
            with open(save_path, 'wb') as f:
                f.write(test_content)
            
            if os.path.exists(save_path):
                print(f"✅ File creation test successful")
                os.unlink(save_path)  # Clean up
                return True
            else:
                print(f"❌ File was not created")
                return False
                
        except Exception as e:
            print(f"❌ File creation failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Filename generation test failed: {e}")
        return False

def test_pdf_creation_no_dialog():
    """Test PDF creation without file dialog."""
    print(f"\n📄 Testing PDF Creation (No Dialog)")
    print("=" * 50)
    
    try:
        sys.path.append('.')
        from roof_material_tool import MaterialListPDF, calculate_materials
        import pandas as pd
        import datetime
        
        # Create sample data
        sample_data = {
            "vendor": "test",
            "extracted_text_length": 100,
            "total_sqft": 2000,
            "ridge_length": 120,
            "hip_length": 45,
            "valley_length": 50,
            "eaves_length": 180
        }
        
        sample_parts = [
            {"part_number": "TEST-001", "description": "Test Panel", "source_metric": "total_sqft", "coverage": 100, "waste": 0.10, "unit": "sq ft"},
            {"part_number": "TEST-002", "description": "Test Ridge", "source_metric": "ridge_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        ]
        
        print("🧮 Calculating materials...")
        results = calculate_materials(sample_data, sample_parts)
        
        if not results:
            print("❌ No materials calculated")
            return False
        
        print(f"✅ Calculated {len(results)} materials")
        
        # Generate automatic filename
        product = "Test Product"
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_product_name = "".join(c for c in product if c.isalnum() or c in (' ', '-', '_')).rstrip()
        safe_product_name = safe_product_name.replace(' ', '_')
        save_path = f"{safe_product_name}_Material_List_{timestamp}.pdf"
        
        print(f"📄 Creating PDF: {save_path}")
        
        # Create PDF using the same logic as the main app
        try:
            pdf = MaterialListPDF()
            pdf.add_page()
            
            # Set font safely
            try:
                pdf.set_font("Arial", "", 10)
            except:
                pdf.set_font("Arial", "", 12)
            
            # Add header information safely
            try:
                pdf.cell(0, 10, f"Vendor: {sample_data.get('vendor', 'unknown')}", ln=1)
                pdf.cell(0, 10, f"Product: {product}", ln=1)
                pdf.cell(0, 10, f"Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
                pdf.cell(0, 10, f"Text Length: {sample_data.get('extracted_text_length', 0)} chars", ln=1)
                pdf.ln(5)
            except Exception as header_error:
                print(f"Header error: {header_error}")
                pdf.cell(0, 10, "Material List Report", ln=1)
                pdf.ln(5)
            
            # Add the materials table safely
            try:
                if results and len(results) > 0:
                    pdf.add_table(results)
                else:
                    pdf.cell(0, 10, "No materials calculated", ln=1)
            except Exception as table_error:
                print(f"Table error: {table_error}")
                pdf.cell(0, 10, f"Error creating table: {str(table_error)}", ln=1)
            
            # Save the PDF safely using multiple methods
            saved = False
            
            # Method 1: Simple output with 'F' flag
            try:
                pdf.output(save_path, 'F')
                if os.path.exists(save_path) and os.path.getsize(save_path) > 1000:
                    print("✅ PDF saved successfully (Method 1)")
                    saved = True
            except Exception as e:
                print(f"Method 1 failed: {e}")
            
            # Method 2: Simple output without flag
            if not saved:
                try:
                    pdf.output(save_path)
                    if os.path.exists(save_path) and os.path.getsize(save_path) > 1000:
                        print("✅ PDF saved successfully (Method 2)")
                        saved = True
                except Exception as e:
                    print(f"Method 2 failed: {e}")
            
            # Method 3: String output
            if not saved:
                try:
                    pdf_string = pdf.output(dest='S')
                    with open(save_path, 'wb') as f:
                        if isinstance(pdf_string, str):
                            f.write(pdf_string.encode('latin1'))
                        else:
                            f.write(pdf_string)
                    
                    if os.path.exists(save_path) and os.path.getsize(save_path) > 1000:
                        print("✅ PDF saved successfully (Method 3)")
                        saved = True
                except Exception as e:
                    print(f"Method 3 failed: {e}")
            
            if saved:
                file_size = os.path.getsize(save_path)
                print(f"📊 PDF file size: {file_size} bytes")
                print(f"📁 PDF location: {os.path.abspath(save_path)}")
                
                # Clean up
                os.unlink(save_path)
                return True
            else:
                print("❌ All PDF save methods failed")
                return False
                
        except Exception as pdf_error:
            print(f"❌ PDF creation failed: {pdf_error}")
            return False
            
    except Exception as e:
        print(f"❌ PDF test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🏠 Isaiah Industries - No Dialog Fix Test")
    print("🔧 Testing the fix that removes problematic file dialogs")
    
    filename_test = test_automatic_filename()
    pdf_test = test_pdf_creation_no_dialog()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print(f"✅ Filename Generation: {'PASS' if filename_test else 'FAIL'}")
    print(f"✅ PDF Creation: {'PASS' if pdf_test else 'FAIL'}")
    
    if filename_test and pdf_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The no-dialog fix should work correctly.")
        print(f"\n💡 Key improvements:")
        print("• No more file dialog issues")
        print("• Automatic filename generation")
        print("• Saves to Desktop or current directory")
        print("• Multiple PDF save methods as fallbacks")
        print("• Clear feedback about save location")
    else:
        print(f"\n⚠️ Some tests failed.")
    
    print(f"\n🚀 The tool will now:")
    print("• Skip the problematic file dialog completely")
    print("• Generate a timestamped filename automatically")
    print("• Save to your Desktop (or current folder)")
    print("• Show you exactly where the file was saved")
    print("• No more '_querystring' errors!")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
