#!/bin/bash

echo "========================================"
echo "Isaiah Industries Roof Tool - Mac Builder"
echo "Modern Professional Version 2.0"
echo "========================================"
echo

echo "Installing required packages..."
pip3 install -r requirements.txt

echo
echo "Creating application icon..."
python3 create_icon.py

echo
echo "Building Mac application..."
if [ -f "icon.ico" ]; then
    # Convert ICO to ICNS for Mac
    if command -v sips &> /dev/null; then
        sips -s format icns icon.ico --out icon.icns
        pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --icon=icon.icns --add-data "parts.csv:." roof_material_tool.py
    else
        pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv:." roof_material_tool.py
    fi
else
    pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv:." roof_material_tool.py
fi

echo
echo "========================================"
echo "Build Complete!"
echo "========================================"
echo "Your Mac application is in the 'dist' folder:"
echo "dist/Isaiah_Industries_Roof_Tool"
echo
echo "NEW FEATURES:"
echo "- Modern professional UI design"
echo "- Improved user experience"
echo "- Commercial-grade appearance"
echo "- Enhanced error handling"
echo "- Professional PDF output"
echo
echo "To distribute to Mac contractors:"
echo "1. Copy the app file from dist/ folder"
echo "2. Install Tesseract OCR on target Macs"
echo "3. Run the app"
echo "4. Ready for commercial use!"
echo
echo "Press any key to continue..."
read -n 1
