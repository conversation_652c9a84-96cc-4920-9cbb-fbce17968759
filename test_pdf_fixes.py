"""
Test the PDF extraction and generation fixes
"""
import sys
import os
import tempfile

def test_pdf_generation():
    """Test PDF generation with sample data to check for errors."""
    print("🧪 Testing PDF Generation Fix")
    print("=" * 50)
    
    try:
        # Import the classes
        sys.path.append('.')
        from roof_material_tool import MaterialListPDF, calculate_materials
        import pandas as pd
        
        # Create sample data
        sample_data = {
            "vendor": "test",
            "extracted_text_length": 100,
            "total_sqft": 2000,
            "ridge_length": 120,
            "hip_length": 45,
            "valley_length": 50,
            "eaves_length": 180
        }
        
        sample_parts = [
            {"part_number": "TEST-001", "description": "Test Panel", "source_metric": "total_sqft", "coverage": 100, "waste": 0.10, "unit": "sq ft"},
            {"part_number": "TEST-002", "description": "Test Ridge", "source_metric": "ridge_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        ]
        
        print(f"📋 Sample data created")
        print(f"  • Measurements: {len([k for k, v in sample_data.items() if isinstance(v, int) and k != 'extracted_text_length'])}")
        print(f"  • Parts: {len(sample_parts)}")
        
        # Test material calculation
        print(f"\n🧮 Testing material calculations...")
        results = calculate_materials(sample_data, sample_parts)
        
        if results:
            print(f"✅ Calculated {len(results)} material items:")
            for result in results:
                print(f"  • {result['part_number']}: {result['description']} - Qty: {result['count']}")
        else:
            print(f"❌ No materials calculated")
            return False
        
        # Test PDF creation
        print(f"\n📄 Testing PDF creation...")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            pdf = MaterialListPDF()
            pdf.add_page()
            pdf.set_font("Arial", "", 10)
            
            # Add header information (test the exact code from the main app)
            pdf.cell(0, 10, f"Vendor Detected: {sample_data.get('vendor', 'unknown')}", ln=1)
            pdf.cell(0, 10, f"Product Line: Test Product", ln=1)
            pdf.cell(0, 10, f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
            pdf.cell(0, 10, f"Text Extracted: {sample_data.get('extracted_text_length', 0)} characters", ln=1)
            pdf.ln(5)
            
            # Add the materials table
            if results and len(results) > 0:
                pdf.add_table(results)
            else:
                pdf.cell(0, 10, "No materials calculated", ln=1)
            
            # Save the PDF
            pdf.output(temp_path)
            
            print(f"✅ PDF created successfully!")
            
            # Check if file exists and has content
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                print(f"  • File size: {file_size} bytes")
                print(f"  • File path: {temp_path}")
                
                if file_size > 1000:  # Should be at least 1KB for a valid PDF
                    print(f"✅ PDF appears to be valid")
                    return True
                else:
                    print(f"⚠️ PDF file is very small, might be corrupted")
                    return False
            else:
                print(f"❌ PDF file was not created")
                return False
                
        except Exception as e:
            print(f"❌ PDF creation failed: {e}")
            print(f"Error type: {type(e).__name__}")
            return False
        finally:
            # Clean up
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except:
                pass
                
    except ImportError as e:
        print(f"❌ Could not import required modules: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_enhanced_extraction():
    """Test the enhanced PDF extraction methods."""
    print(f"\n🔍 Testing Enhanced PDF Extraction")
    print("=" * 50)
    
    try:
        sys.path.append('.')
        from roof_material_tool import extract_text_from_chars, analyze_text_for_measurements
        
        # Test character extraction
        print(f"📝 Testing character extraction...")
        sample_chars = [
            {'text': 'T', 'top': 100, 'x0': 10},
            {'text': 'o', 'top': 100, 'x0': 15},
            {'text': 't', 'top': 100, 'x0': 20},
            {'text': 'a', 'top': 100, 'x0': 25},
            {'text': 'l', 'top': 100, 'x0': 30},
            {'text': ':', 'top': 100, 'x0': 35},
            {'text': ' ', 'top': 100, 'x0': 40},
            {'text': '2', 'top': 100, 'x0': 45},
            {'text': '0', 'top': 100, 'x0': 50},
            {'text': '0', 'top': 100, 'x0': 55},
            {'text': '0', 'top': 100, 'x0': 60},
            {'text': 'R', 'top': 120, 'x0': 10},  # New line
            {'text': 'i', 'top': 120, 'x0': 15},
            {'text': 'd', 'top': 120, 'x0': 20},
            {'text': 'g', 'top': 120, 'x0': 25},
            {'text': 'e', 'top': 120, 'x0': 30},
            {'text': ':', 'top': 120, 'x0': 35},
            {'text': ' ', 'top': 120, 'x0': 40},
            {'text': '1', 'top': 120, 'x0': 45},
            {'text': '2', 'top': 120, 'x0': 50},
            {'text': '0', 'top': 120, 'x0': 55},
        ]
        
        extracted_text = extract_text_from_chars(sample_chars)
        print(f"✅ Character extraction result:")
        print(f"  '{extracted_text}'")
        
        if "Total: 2000" in extracted_text and "Ridge: 120" in extracted_text:
            print(f"✅ Character extraction working correctly")
        else:
            print(f"⚠️ Character extraction may have issues")
        
        # Test text analysis
        print(f"\n🔍 Testing text analysis...")
        sample_texts = [
            "Total Roof Area: 2,450 sq ft\nRidge Length: 125 ft\nHip: 45 feet\nValley Length = 78 ft\nEave Length: 180 ft",
            "Building Area 1850 square feet Ridge 95 linear feet Valley 65 ft Perimeter 220 feet",
            "Area: 2100 SF Ridge: 110 ft Hip: 38 ft Valley: 55 ft Eave: 195 ft"
        ]
        
        for i, text in enumerate(sample_texts):
            print(f"\n  Testing sample {i+1}:")
            print(f"    Text: '{text[:50]}...'")
            
            result = analyze_text_for_measurements(text)
            
            measurements_found = sum(1 for v in result.values() if isinstance(v, int) and v > 0)
            print(f"    Found {measurements_found} measurements:")
            
            for key, value in result.items():
                if isinstance(value, int) and value > 0:
                    print(f"      • {key}: {value}")
        
        print(f"\n✅ Enhanced extraction methods tested")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced extraction test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🏠 Isaiah Industries - PDF Fixes Test")
    print("🔧 Testing PDF generation and extraction fixes")
    
    pdf_test_passed = test_pdf_generation()
    extraction_test_passed = test_enhanced_extraction()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print(f"✅ PDF Generation: {'PASSED' if pdf_test_passed else 'FAILED'}")
    print(f"✅ Enhanced Extraction: {'PASSED' if extraction_test_passed else 'FAILED'}")
    
    if pdf_test_passed and extraction_test_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The PDF generation error should be fixed.")
        print(f"The enhanced extraction should handle more PDF types.")
    else:
        print(f"\n⚠️ Some tests failed. Check the errors above.")
    
    print(f"\n💡 Next steps:")
    print("1. Rebuild the application: build.bat")
    print("2. Test with your problematic PDFs")
    print("3. The tool should now extract text from more PDF types")
    print("4. PDF generation should work without errors")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
