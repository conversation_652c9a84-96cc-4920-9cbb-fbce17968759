# 🏗️ Building Isaiah Industries Roof Material Tool EXE

## Prerequisites

1. **Python 3.8+** installed on your system
2. **Tesseract OCR** installed (for OCR functionality)
   - Download from: https://github.com/UB-Mannheim/tesseract/wiki
   - Install and add to PATH

## Quick Build (Automated)

1. Open Command Prompt or PowerShell in this directory
2. Run the build script:
   ```bash
   python build_exe.py
   ```

## Manual Build Steps

If you prefer to build manually:

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Build the executable:**
   ```bash
   pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv;." roof_material_tool.py
   ```

## Distribution

### What to Share with Coworkers:

1. **The EXE file**: `dist/Isaiah_Industries_Roof_Tool.exe`
2. **Tesseract OCR installer** (if they don't have it)

### Installation Instructions for Coworkers:

1. **Install Tesseract OCR:**
   - Download: https://github.com/UB-Mannheim/tesseract/wiki
   - Run installer with default settings
   - Restart computer after installation

2. **Run the Tool:**
   - Double-click `Isaiah_Industries_Roof_Tool.exe`
   - No other installation required!

## Troubleshooting

### Common Issues:

1. **"Tesseract not found" error:**
   - Install Tesseract OCR and restart
   - Ensure it's added to system PATH

2. **"Missing DLL" errors:**
   - Install Microsoft Visual C++ Redistributable
   - Download from Microsoft's website

3. **Antivirus blocking:**
   - Some antivirus software may flag PyInstaller executables
   - Add exception or whitelist the file

### File Size:
- The EXE will be approximately 50-100MB due to included libraries
- This is normal for Python applications with PDF/OCR capabilities

## Features Included:

✅ PDF text extraction  
✅ OCR fallback for scanned PDFs  
✅ Isaiah Industries branding  
✅ Material calculation  
✅ Professional PDF output  
✅ Parts database (embedded)  

## Support:

If coworkers have issues, they can:
1. Check Tesseract OCR installation
2. Try running as administrator
3. Ensure Windows is up to date
