# 🏠 Isaiah Industries - Roof Material Analysis Tool

## 📥 Installation (One-time setup)

### Step 1: Install Tesseract OCR
1. Download Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki
2. Run the installer with default settings
3. **Restart your computer** after installation

### Step 2: Get the Tool
1. Copy `Isaiah_Industries_Roof_Tool.exe` to your computer
2. Place it in any folder (Desktop works fine)

## 🚀 How to Use

1. **Double-click** `Isaiah_Industries_Roof_Tool.exe`
2. **Select Product Line** from the dropdown
3. **Click** "📄 Upload Roof Report & Generate Material List"
4. **Choose** your PDF roof report file
5. **Fill in** any missing measurements if prompted
6. **Save** the generated material list PDF

## 📋 Supported Roof Report Vendors

✅ EagleView  
✅ RoofSnap  
✅ Hover  
✅ Roofr  
✅ Any PDF with roof measurements  

## 🔧 Troubleshooting

### Tool becomes unresponsive:
- **Fixed!** The tool now processes PDFs in the background
- You'll see progress updates in the status bar
- The button will show "Processing..." while working

### "Tesseract not found" or "OCR Error":
1. **Install Tesseract OCR:**
   - Download: https://github.com/UB-<PERSON>heim/tesseract/wiki
   - Choose "tesseract-ocr-w64-setup-5.3.3.20231005.exe" (or latest)
   - Install with **default settings** (important!)
   - **Restart your computer** after installation

2. **Test your installation:**
   - Run `test_ocr.py` (if provided) to check if everything works
   - Or try the main tool with a simple PDF first

### "Failed to extract data with OCR":
- The PDF might be corrupted or very complex
- Try a different PDF file first
- Make sure the PDF actually contains text/measurements
- Some PDFs are just images and may not work well with OCR

### Tool won't start:
- Right-click the .exe and select "Run as administrator"
- Make sure Windows is up to date
- Check that antivirus isn't blocking the file

### Antivirus blocking the file:
- Add the .exe file to your antivirus exceptions
- This is normal for packaged Python applications

### No measurements found:
- The tool will prompt you to enter missing measurements manually
- This is normal if the PDF format isn't recognized
- Enter measurements in feet when prompted

## 📞 Support

If you have issues:
1. Check that Tesseract OCR is installed
2. Try restarting your computer
3. Contact IT support

---
*© 2024 Isaiah Industries - Roof Material Analysis Tool*
