# 🏠 Isaiah Industries - Roof Material Analysis Tool

## 📥 Installation (One-time setup)

### Step 1: Install Tesseract OCR
1. Download Tesseract OCR: https://github.com/UB-Mannheim/tesseract/wiki
2. Run the installer with default settings
3. **Restart your computer** after installation

### Step 2: Get the Tool
1. Copy `Isaiah_Industries_Roof_Tool.exe` to your computer
2. Place it in any folder (Desktop works fine)

## 🚀 How to Use

1. **Double-click** `Isaiah_Industries_Roof_Tool.exe`
2. **Select Product Line** from the dropdown
3. **Click** "📄 Upload Roof Report & Generate Material List"
4. **Choose** your PDF roof report file
5. **Fill in** any missing measurements if prompted
6. **Save** the generated material list PDF

## 📋 Supported Roof Report Vendors

✅ EagleView  
✅ RoofSnap  
✅ Hover  
✅ Roofr  
✅ Any PDF with roof measurements  

## 🔧 Troubleshooting

### "Tesseract not found" error:
- Make sure Tesseract OCR is installed
- Restart your computer after installation
- Try running the tool as administrator

### Antivirus blocking the file:
- Add the .exe file to your antivirus exceptions
- This is normal for packaged Python applications

### Tool won't start:
- Right-click the .exe and select "Run as administrator"
- Make sure Windows is up to date

## 📞 Support

If you have issues:
1. Check that Tesseract OCR is installed
2. Try restarting your computer
3. Contact IT support

---
*© 2024 Isaiah Industries - Roof Material Analysis Tool*
