"""
Show where PDFs are being saved
"""
import os
import datetime

def show_pdf_save_location():
    """Show exactly where PDFs will be saved."""
    print("🔍 PDF Save Location Finder")
    print("=" * 40)
    
    # Test the same logic as the main app
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    product = "Country_Manor_Shake"  # Example product
    safe_product_name = "".join(c for c in product if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_product_name = safe_product_name.replace(' ', '_')
    
    print(f"📋 Example product: {product}")
    print(f"📋 Safe filename: {safe_product_name}")
    print(f"📋 Timestamp: {timestamp}")
    
    # Check Desktop path
    desktop = os.path.join(os.path.expanduser("~"), "Desktop")
    print(f"\n📁 Desktop path: {desktop}")
    print(f"📁 Desktop exists: {os.path.exists(desktop)}")
    
    # Determine save location
    if os.path.exists(desktop):
        save_path = os.path.join(desktop, f"{safe_product_name}_Material_List_{timestamp}.pdf")
        save_location = "Desktop"
    else:
        save_path = f"{safe_product_name}_Material_List_{timestamp}.pdf"
        save_location = "current directory"
    
    print(f"\n🎯 WHERE YOUR PDFs ARE SAVED:")
    print(f"📍 Location: {save_location}")
    print(f"📄 Example filename: {os.path.basename(save_path)}")
    print(f"🗂️ Full path: {save_path}")
    
    # Check if we can write to that location
    try:
        test_file = os.path.join(os.path.dirname(save_path), "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.unlink(test_file)
        print(f"✅ Can write to this location")
    except Exception as e:
        print(f"❌ Cannot write to this location: {e}")
    
    # Show current directory for reference
    current_dir = os.getcwd()
    print(f"\n📂 Current directory: {current_dir}")
    
    # List any existing PDF files in the save location
    save_dir = os.path.dirname(save_path) if os.path.dirname(save_path) else current_dir
    print(f"\n📋 Existing PDF files in {save_location}:")
    
    try:
        pdf_files = [f for f in os.listdir(save_dir) if f.lower().endswith('.pdf')]
        if pdf_files:
            for pdf in pdf_files[-5:]:  # Show last 5 PDFs
                pdf_path = os.path.join(save_dir, pdf)
                size = os.path.getsize(pdf_path)
                modified = datetime.datetime.fromtimestamp(os.path.getmtime(pdf_path))
                print(f"  📄 {pdf} ({size:,} bytes, {modified.strftime('%Y-%m-%d %H:%M')})")
        else:
            print(f"  (No PDF files found)")
    except Exception as e:
        print(f"  ❌ Could not list files: {e}")
    
    return save_path, save_location

def open_save_location():
    """Open the save location in file explorer."""
    save_path, save_location = show_pdf_save_location()
    
    print(f"\n🚀 Opening {save_location} in file explorer...")
    
    try:
        import subprocess
        import platform
        
        save_dir = os.path.dirname(save_path) if os.path.dirname(save_path) else os.getcwd()
        
        if platform.system() == "Windows":
            subprocess.run(f'explorer "{save_dir}"', shell=True)
            print(f"✅ Opened Windows Explorer")
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", save_dir])
            print(f"✅ Opened Finder")
        else:  # Linux
            subprocess.run(["xdg-open", save_dir])
            print(f"✅ Opened file manager")
            
    except Exception as e:
        print(f"❌ Could not open file explorer: {e}")
        print(f"💡 Manually navigate to: {save_dir}")

def main():
    """Main function."""
    print("🏠 Isaiah Industries - PDF Location Finder")
    print("🔍 Find out exactly where your PDFs are being saved")
    
    save_path, save_location = show_pdf_save_location()
    
    print(f"\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print(f"📍 Your PDFs are saved to: {save_location}")
    print(f"📁 Look in this folder: {os.path.dirname(save_path) if os.path.dirname(save_path) else os.getcwd()}")
    print(f"📄 Filenames look like: ProductName_Material_List_YYYYMMDD_HHMMSS.pdf")
    
    print(f"\n💡 Tips:")
    print("• PDFs are automatically named with timestamp")
    print("• They save to your Desktop if it exists")
    print("• Otherwise they save to the program folder")
    print("• The success dialog shows the exact location")
    print("• Click 'Yes' in the success dialog to open the folder")
    
    choice = input(f"\nWould you like to open the {save_location} folder now? (y/n): ")
    if choice.lower().startswith('y'):
        open_save_location()
    
    print(f"\n🎯 Next time you generate a PDF:")
    print("1. Look for the success dialog")
    print("2. Note the filename and location")
    print("3. Click 'Yes' to open the folder automatically")
    print("4. Or manually navigate to the location shown above")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
