"""
Safe launcher for the roof material tool - ensures correct version is used
"""
import sys
import os

def main():
    """Launch the roof material tool safely."""
    print("🏠 Isaiah Industries - Roof Material Tool Launcher")
    print("🔧 Ensuring you run the correct version...")
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Check for main file
    main_file = "roof_material_tool.py"
    if not os.path.exists(main_file):
        print(f"❌ {main_file} not found!")
        print(f"Make sure you're in the correct directory.")
        input("Press Enter to exit...")
        return
    
    # Quick version check
    with open(main_file, 'r') as f:
        content = f.read()
    
    # Check for problematic imports
    if "pytesseract" in content or "pdf2image" in content:
        print(f"❌ WRONG VERSION DETECTED!")
        print(f"The file contains OCR dependencies that will cause errors.")
        print(f"Please use the fixed version without OCR.")
        input("Press Enter to exit...")
        return
    
    print(f"✅ Correct version detected - launching tool...")
    
    # Import and run the tool
    try:
        from roof_material_tool import run_gui
        print(f"🚀 Starting Isaiah Industries Roof Material Tool...")
        run_gui()
    except ImportError as e:
        print(f"❌ Import error: {e}")
        if "pytesseract" in str(e) or "pdf2image" in str(e):
            print(f"This confirms OCR dependencies are causing issues.")
            print(f"Please use the fixed version without OCR.")
        input("Press Enter to exit...")
    except Exception as e:
        print(f"❌ Error starting tool: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
