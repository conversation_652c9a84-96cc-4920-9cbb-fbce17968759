---
name: Bug Report
about: Create a report to help us improve the Isaiah Industries Roof Tool
title: '[BUG] '
labels: bug
assignees: ''

---

## 🐛 **Bug Description**
A clear and concise description of what the bug is.

## 🔄 **Steps to Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ **Expected Behavior**
A clear and concise description of what you expected to happen.

## 📸 **Screenshots**
If applicable, add screenshots to help explain your problem.

## 💻 **Environment**
- **OS**: [e.g. Windows 10, macOS 12.0]
- **Tool Version**: [e.g. v2.0]
- **PDF Vendor**: [e.g. EagleView, RoofSnap]
- **Python Version** (if running from source): [e.g. 3.11]

## 📄 **PDF Information** (if applicable)
- **Vendor**: [e.g. EagleView]
- **File Size**: [e.g. 2.5MB]
- **Number of Pages**: [e.g. 3]
- **Is it text-based or scanned?**: [e.g. text-based]

## 📋 **Additional Context**
Add any other context about the problem here.

## 🔧 **Possible Solution** (optional)
If you have ideas on how to fix this, please share them.

---

**For AI Agents**: This issue is ready for automated analysis and resolution. Please:
1. Reproduce the issue
2. Identify the root cause
3. Implement a fix
4. Add tests to prevent regression
5. Update documentation if needed
