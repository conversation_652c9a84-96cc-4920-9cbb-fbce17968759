"""
Debug tool to see exactly what's being extracted from your PDF
"""
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext
import re

def debug_pdf_extraction():
    """Debug PDF extraction to see exactly what's happening."""
    print("🔍 PDF Extraction Debug Tool")
    print("=" * 50)
    
    # Select PDF file
    root = tk.Tk()
    root.withdraw()
    
    pdf_path = filedialog.askopenfilename(
        title="Select PDF to Debug",
        filetypes=[("PDF Files", "*.pdf")]
    )
    
    if not pdf_path:
        print("No PDF selected")
        return
    
    print(f"📄 Debugging: {pdf_path}")
    
    try:
        import sys
        sys.path.append('.')
        from roof_material_tool import extract_roof_data
        
        # Create debug window
        debug_window = tk.Toplevel()
        debug_window.title("PDF Extraction Debug Results")
        debug_window.geometry("1000x700")
        
        # Create text area
        text_area = scrolledtext.ScrolledText(debug_window, wrap=tk.WORD, width=120, height=40)
        text_area.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        def debug_callback(msg):
            text_area.insert(tk.END, f"{msg}\n")
            text_area.see(tk.END)
            debug_window.update()
        
        debug_callback("🚀 Starting PDF extraction debug...")
        debug_callback("=" * 60)
        
        # Try extraction
        result = extract_roof_data(pdf_path, debug_callback)
        
        debug_callback("\n" + "=" * 60)
        debug_callback("📊 FINAL RESULTS:")
        debug_callback("=" * 60)
        
        if result:
            for key, value in result.items():
                debug_callback(f"• {key}: {value}")
            
            # Show what measurements were found
            measurements = {k: v for k, v in result.items() if isinstance(v, int) and k != 'extracted_text_length'}
            debug_callback(f"\n🎯 MEASUREMENTS FOUND: {len(measurements)}")
            
            if measurements:
                debug_callback("✅ SUCCESS! Found these measurements:")
                for field, value in measurements.items():
                    debug_callback(f"  • {field.replace('_', ' ').title()}: {value}")
            else:
                debug_callback("❌ NO MEASUREMENTS FOUND")
                debug_callback("\n💡 This means the PDF has text but no recognizable measurements.")
                debug_callback("   The patterns didn't match the format in this PDF.")
        else:
            debug_callback("❌ EXTRACTION FAILED - No results returned")
        
        debug_callback("\n" + "=" * 60)
        debug_callback("🔧 TROUBLESHOOTING TIPS:")
        debug_callback("=" * 60)
        debug_callback("1. If you see text but no measurements:")
        debug_callback("   • The PDF format may be unusual")
        debug_callback("   • Try exporting a new PDF from your software")
        debug_callback("   • Use 'Export Report' not 'Save Image'")
        debug_callback("")
        debug_callback("2. If you see very little text:")
        debug_callback("   • The PDF may be image-based")
        debug_callback("   • Try getting a text-based PDF from your software")
        debug_callback("")
        debug_callback("3. If extraction fails completely:")
        debug_callback("   • The PDF may be encrypted or corrupted")
        debug_callback("   • Try opening and re-saving the PDF")
        
        # Keep window open
        debug_window.deiconify()
        debug_window.mainloop()
        
    except Exception as e:
        messagebox.showerror("Debug Error", f"Debug failed: {e}")
        print(f"Debug error: {e}")

def test_pattern_matching():
    """Test pattern matching with sample text."""
    print("\n🧪 Testing Pattern Matching")
    print("=" * 50)
    
    # Sample texts from various PDF formats
    test_texts = [
        # Standard formats
        "Total Roof Area: 2,450 sq ft\nRidge Length: 125 ft\nHip: 45 feet\nValley Length = 78 ft\nEave Length: 180 ft",
        
        # Table format
        "Area | 2100 sq ft\nRidge | 110 ft\nHip | 38 ft\nValley | 55 ft\nEave | 195 ft",
        
        # Scattered format
        "Building area 1850 square feet ridge 95 linear feet valley 65 ft perimeter 220 feet",
        
        # Vendor specific
        "EagleView Report\nRoof Surface Area: 2300 SF\nRidge Line: 130 ft\nHip Line: 42 ft\nValley: 68 ft\nGutter Length: 210 ft",
        
        # Numbers without clear labels
        "2450 1250 125 45 78 180",
        
        # Mixed format
        "Total: 2100\nRidge 110\nHip 38\nValley 55\nPerimeter 195"
    ]
    
    try:
        import sys
        sys.path.append('.')
        from roof_material_tool import analyze_text_for_measurements
        
        for i, text in enumerate(test_texts):
            print(f"\n📝 Test {i+1}: {text[:50]}...")
            
            result = analyze_text_for_measurements(text)
            
            measurements_found = sum(1 for v in result.values() if isinstance(v, int) and v > 0)
            print(f"   Found {measurements_found} measurements:")
            
            for key, value in result.items():
                if isinstance(value, int) and value > 0:
                    print(f"     • {key}: {value}")
            
            if measurements_found == 0:
                print(f"     ❌ No measurements detected")
    
    except Exception as e:
        print(f"Pattern test error: {e}")

def show_extraction_methods():
    """Show what extraction methods are available."""
    print("\n🔧 Available Extraction Methods")
    print("=" * 50)
    
    methods = [
        "1. Standard Text Extraction - Basic pdfplumber text extraction",
        "2. Layout-Preserved Extraction - Maintains spatial relationships", 
        "3. Character-Level Extraction - Extracts from individual characters",
        "4. Word-Level Extraction - Processes word objects with positioning",
        "5. Table Extraction - Multiple table detection strategies",
        "6. Alternative Settings - Different tolerance settings",
        "7. Aggressive Pattern Matching - 40+ measurement patterns",
        "8. Smart Number Guessing - Finds numbers even without labels"
    ]
    
    for method in methods:
        print(f"  {method}")
    
    print(f"\n💡 The tool tries ALL these methods on your PDF!")

def main():
    """Main debug interface."""
    print("🏠 Isaiah Industries - PDF Extraction Debug Tool")
    print("🔍 This tool will show you exactly what's being extracted from your PDF")
    
    while True:
        print(f"\n" + "=" * 60)
        print("Choose an option:")
        print("1. Debug a specific PDF file")
        print("2. Test pattern matching with sample text")
        print("3. Show available extraction methods")
        print("4. Exit")
        
        choice = input("\nEnter choice (1-4): ").strip()
        
        if choice == "1":
            debug_pdf_extraction()
        elif choice == "2":
            test_pattern_matching()
        elif choice == "3":
            show_extraction_methods()
        elif choice == "4":
            break
        else:
            print("Invalid choice. Please enter 1-4.")

if __name__ == "__main__":
    main()
