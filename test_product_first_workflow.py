"""
Test the improved workflow: Product First
"""
import sys
import os

def test_workflow_logic():
    """Test the new workflow logic."""
    print("🧪 Testing Product-First Workflow")
    print("=" * 50)
    
    print("📋 New Workflow:")
    print("1. ✅ Choose Product First (on main thread)")
    print("2. ✅ Select PDF File (on main thread)")
    print("3. ✅ Extract Data (no dialogs)")
    print("4. ✅ Generate Materials (no dialogs)")
    print("5. ✅ Create PDF (automatic filename)")
    
    print(f"\n💡 Benefits:")
    print("• No threading issues with dialogs")
    print("• Better user experience")
    print("• Faster processing")
    print("• More logical workflow")
    print("• Eliminates '_querystring' errors")
    
    return True

def test_product_selection():
    """Test product selection logic."""
    print(f"\n🏠 Testing Product Selection")
    print("=" * 30)
    
    try:
        sys.path.append('.')
        from roof_material_tool import PRODUCTS, load_parts
        
        print(f"📋 Available products: {PRODUCTS}")
        
        # Test loading parts for each product
        for product in PRODUCTS:
            print(f"\n🔍 Testing: {product}")
            parts = load_parts(product)
            
            if parts:
                print(f"  ✅ Found {len(parts)} parts")
                # Show first few parts
                for part in parts[:3]:
                    print(f"    • {part.get('part_number', 'N/A')}: {part.get('description', 'N/A')}")
            else:
                print(f"  ❌ No parts found")
        
        return True
        
    except Exception as e:
        print(f"❌ Product selection test failed: {e}")
        return False

def test_automatic_filename():
    """Test automatic filename generation."""
    print(f"\n📄 Testing Automatic Filename Generation")
    print("=" * 40)
    
    try:
        import datetime
        
        # Test with different product names
        test_products = [
            "Country Manor Shake",
            "Oxford Shingle",
            "Test Product with Spaces",
            "Product-With-Dashes",
            "Product_With_Underscores"
        ]
        
        for product in test_products:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_product_name = "".join(c for c in product if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_product_name = safe_product_name.replace(' ', '_')
            
            filename = f"{safe_product_name}_Material_List_{timestamp}.pdf"
            
            print(f"📋 Product: '{product}'")
            print(f"   Filename: '{filename}'")
            
            # Test that filename is valid
            if len(filename) > 0 and not any(c in filename for c in ['<', '>', ':', '"', '|', '?', '*']):
                print(f"   ✅ Valid filename")
            else:
                print(f"   ❌ Invalid filename")
        
        # Test desktop path detection
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
        print(f"\n📁 Desktop path: {desktop}")
        print(f"📁 Desktop exists: {os.path.exists(desktop)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Filename test failed: {e}")
        return False

def test_no_threading():
    """Test that we're not using threading anymore."""
    print(f"\n🧵 Testing No Threading")
    print("=" * 25)
    
    try:
        # Check if the main file imports threading
        with open('roof_material_tool.py', 'r') as f:
            content = f.read()
        
        has_threading_import = "import threading" in content
        has_thread_start = "thread.start()" in content
        has_background_thread = "Thread(target=" in content
        
        print(f"📋 Threading Analysis:")
        print(f"  • Has threading import: {'❌ YES (BAD)' if has_threading_import else '✅ NO (GOOD)'}")
        print(f"  • Has thread.start(): {'❌ YES (BAD)' if has_thread_start else '✅ NO (GOOD)'}")
        print(f"  • Has background threads: {'❌ YES (BAD)' if has_background_thread else '✅ NO (GOOD)'}")
        
        if not has_threading_import and not has_thread_start and not has_background_thread:
            print(f"\n✅ No threading detected - dialogs will run on main thread!")
            return True
        else:
            print(f"\n⚠️ Threading still detected - may cause dialog issues")
            return False
            
    except Exception as e:
        print(f"❌ Threading test failed: {e}")
        return False

def main():
    """Run all workflow tests."""
    print("🏠 Isaiah Industries - Product-First Workflow Test")
    print("🔧 Testing the improved workflow that eliminates dialog errors")
    
    workflow_test = test_workflow_logic()
    product_test = test_product_selection()
    filename_test = test_automatic_filename()
    threading_test = test_no_threading()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print(f"✅ Workflow Logic: {'PASS' if workflow_test else 'FAIL'}")
    print(f"✅ Product Selection: {'PASS' if product_test else 'FAIL'}")
    print(f"✅ Filename Generation: {'PASS' if filename_test else 'FAIL'}")
    print(f"✅ No Threading: {'PASS' if threading_test else 'FAIL'}")
    
    all_passed = workflow_test and product_test and filename_test and threading_test
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The product-first workflow should eliminate dialog errors.")
        print(f"\n💡 Key improvements:")
        print("• Product selection happens FIRST (main thread)")
        print("• No background threading for dialogs")
        print("• Automatic PDF filenames (no save dialog)")
        print("• Cleaner, more logical user experience")
        print("• Should eliminate '_querystring' errors completely")
    else:
        print(f"\n⚠️ Some tests failed - check issues above")
    
    print(f"\n🚀 The new workflow:")
    print("1. User clicks button")
    print("2. Choose product (simple dialog on main thread)")
    print("3. Select PDF file (file dialog on main thread)")
    print("4. Process everything without more dialogs")
    print("5. Auto-save PDF with clear success message")
    
    print(f"\n🎯 This should completely fix the PDF generation error!")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
