import pdfplumber
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
from fpdf import FPDF
import pandas as pd
import re
import math
import os
from typing import List, Dict, Any, Optional
import threading
# OCR dependencies
from pdf2image import convert_from_path
import pytesseract

CSV_PATH = "parts.csv"
COMPANY_NAME = "Isaiah Industries"
PRODUCTS = ["Country Manor Shake", "Oxford Shingle"]

def load_parts(product_name: str, csv_path: str = CSV_PATH) -> List[Dict[str, Any]]:
    """Load parts for a given product from CSV."""
    try:
        df = pd.read_csv(csv_path)
        if "product" not in df.columns:
            raise ValueError("CSV missing 'product' column.")
        return df[df["product"].str.lower() == product_name.lower()].to_dict(orient="records")
    except Exception as e:
        messagebox.showerror("CSV Error", f"Failed to load parts: {e}")
        return []

def check_tesseract_installation() -> bool:
    """Check if Tesseract OCR is properly installed."""
    try:
        # Try to get Tesseract version
        version = pytesseract.get_tesseract_version()
        return True
    except Exception:
        return False

def extract_roof_data(path: str, progress_callback=None) -> Dict[str, Any]:
    """Detect vendor and extract roof data from PDF, using OCR fallback if needed."""
    if progress_callback:
        progress_callback("Reading PDF file...")

    text_all = ""

    # First try: Extract text directly from PDF
    try:
        with pdfplumber.open(path) as pdf:
            if progress_callback:
                progress_callback(f"Processing {len(pdf.pages)} pages...")

            for i, page in enumerate(pdf.pages):
                if progress_callback:
                    progress_callback(f"Reading page {i+1}/{len(pdf.pages)}...")
                page_text = page.extract_text() or ""
                text_all += page_text

    except Exception as e:
        error_msg = f"Failed to read PDF: {str(e)}"
        if progress_callback:
            progress_callback(f"Error: {error_msg}")
        messagebox.showerror("PDF Error", error_msg)
        return {}

    # If no text found, try OCR
    if not text_all.strip():
        if progress_callback:
            progress_callback("No text found in PDF, trying OCR...")

        # Check if Tesseract is installed
        if not check_tesseract_installation():
            error_msg = ("Tesseract OCR is not installed or not found in PATH.\n\n"
                        "Please install Tesseract OCR from:\n"
                        "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
                        "After installation, restart your computer.")
            messagebox.showerror("OCR Error", error_msg)
            return {}

        try:
            if progress_callback:
                progress_callback("Converting PDF to images...")

            # Convert PDF to images with better settings
            images = convert_from_path(
                path,
                dpi=300,  # Higher DPI for better OCR
                first_page=1,
                last_page=5  # Limit to first 5 pages to avoid timeout
            )

            ocr_text = ""
            for i, img in enumerate(images):
                if progress_callback:
                    progress_callback(f"OCR processing image {i+1}/{len(images)}...")

                # Use better OCR configuration
                custom_config = r'--oem 3 --psm 6'
                page_text = pytesseract.image_to_string(img, config=custom_config)
                ocr_text += page_text + "\n"

            text_all = ocr_text

        except Exception as e:
            error_msg = f"OCR processing failed: {str(e)}\n\nPossible solutions:\n1. Install Tesseract OCR\n2. Restart your computer\n3. Try a different PDF file"
            if progress_callback:
                progress_callback(f"OCR Error: {str(e)}")
            messagebox.showerror("OCR Error", error_msg)
            return {}

    if progress_callback:
        progress_callback("Analyzing extracted text...")

    # Detect vendor
    vendor = "unknown"
    if "EagleView" in text_all: vendor = "EagleView"
    elif "RoofSnap" in text_all: vendor = "RoofSnap"
    elif "Hover" in text_all: vendor = "Hover"
    elif "Roofr" in text_all or "Roof Sketch" in text_all: vendor = "Roofr"

    # Extract measurements with improved patterns
    patterns = {
        "total_sqft": [
            r"Total\s+Area[:=]?\s*([\d,]+)",
            r"Roof\s+Area[:=]?\s*([\d,]+)",
            r"Total\s+Square\s+Feet[:=]?\s*([\d,]+)",
            r"(\d{1,5})\s*sq\.?\s*ft\.?\s*total",
            r"(\d{1,5})\s*square\s+feet"
        ],
        "ridge_length": [
            r"Ridges?[:=]?\s*([\d,]+)\s*ft",
            r"Ridge\s+Length[:=]?\s*([\d,]+)",
            r"Ridge[:=]?\s*([\d,]+)"
        ],
        "hip_length": [
            r"Hips?[:=]?\s*([\d,]+)\s*ft",
            r"Hip\s+Length[:=]?\s*([\d,]+)"
        ],
        "valley_length": [
            r"Valleys?[:=]?\s*([\d,]+)\s*ft",
            r"Valley\s+Length[:=]?\s*([\d,]+)"
        ],
        "eaves_length": [
            r"Eaves?[:=]?\s*([\d,]+)\s*ft",
            r"Eave\s+Length[:=]?\s*([\d,]+)",
            r"Gutter\s+Length[:=]?\s*([\d,]+)"
        ],
    }

    result = {"vendor": vendor, "extracted_text_length": len(text_all)}

    for field, pats in patterns.items():
        for pat in pats:
            match = re.search(pat, text_all, re.IGNORECASE)
            if match:
                try:
                    result[field] = int(match.group(1).replace(",", ""))
                    break
                except (ValueError, IndexError):
                    continue
        if field not in result:
            result[field] = None

    if progress_callback:
        progress_callback("Data extraction complete!")

    return result

def prompt_for_missing(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prompt user for any missing data."""
    for k, v in data.items():
        if k != "vendor" and (v is None or v == 0):
            ans = simpledialog.askinteger("Missing Info", f"Enter {k.replace('_',' ')} (in feet):")
            data[k] = ans if ans else 0
    return data

def calculate_materials(data: Dict[str, Any], parts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Calculate materials based on parts list and extracted data."""
    results = []
    for row in parts:
        src_val = data.get(row.get("source_metric", ""), 0)
        try:
            coverage = float(row.get("coverage", 1))
            waste = float(row.get("waste", 0))
        except (TypeError, ValueError):
            coverage, waste = 1, 0
        unit = row.get("unit", "pieces")
        count = math.ceil((src_val / coverage) * (1 + waste)) if coverage else 0
        results.append({
            "part_number": row.get("part_number", ""),
            "description": row.get("description", ""),
            "unit": unit,
            "count": count
        })
    return results

class MaterialListPDF(FPDF):
    """PDF output for material list with Isaiah Industries branding."""
    def header(self):
        self.set_font("Arial", "B", 16)
        self.cell(0, 10, f"{COMPANY_NAME}", 0, 1, "C")
        self.set_font("Arial", "B", 14)
        self.cell(0, 10, "Roof Material List", 0, 1, "C")
        self.ln(5)

    def add_table(self, data: List[Dict[str, Any]]):
        self.set_font("Arial", "", 10)
        col_width = [40, 60, 30, 20]
        headers = ["Part #", "Description", "Unit", "Count"]
        for i, h in enumerate(headers):
            self.cell(col_width[i], 8, h, border=1)
        self.ln()
        for row in data:
            self.cell(col_width[0], 8, str(row.get("part_number", "")), border=1)
            self.cell(col_width[1], 8, str(row.get("description", "")), border=1)
            self.cell(col_width[2], 8, str(row.get("unit", "")), border=1)
            self.cell(col_width[3], 8, str(row.get("count", "")), border=1)
            self.ln()

def run_gui():
    """Main GUI loop with Isaiah Industries branding."""
    root = tk.Tk()
    root.title(f"{COMPANY_NAME} - Roof Material Analysis Tool")
    root.geometry("520x280")

    # Company header
    header_frame = tk.Frame(root)
    header_frame.pack(pady=(10,5))
    tk.Label(header_frame, text=f"{COMPANY_NAME}", font=("Arial", 16, "bold"), fg="navy").pack()
    tk.Label(header_frame, text="Roof Material Analysis Tool", font=("Arial", 12)).pack()

    tk.Label(root, text="Select Product Line:", font=("Arial", 10, "bold")).pack(pady=(15,5))
    prod_var = tk.StringVar(value=PRODUCTS[0])
    combo = ttk.Combobox(root, textvariable=prod_var, values=PRODUCTS, state="readonly", width=30)
    combo.pack(pady=5)

    status_var = tk.StringVar(value="")

    def set_status(msg: str):
        status_var.set(msg)
        root.update_idletasks()

    def generate():
        product = prod_var.get()
        path = filedialog.askopenfilename(
            title="Select Roof Report PDF",
            filetypes=[("PDF Files", "*.pdf")]
        )
        if not path:
            set_status("No PDF selected.")
            return

        # Disable button during processing
        generate_btn.config(state="disabled", text="Processing...")

        def process_pdf():
            """Process PDF in background thread to prevent GUI freezing."""
            try:
                # Extract data with progress updates
                data = extract_roof_data(path, progress_callback=set_status)

                if not data:
                    root.after(0, lambda: set_status("Failed to extract roof data."))
                    root.after(0, lambda: generate_btn.config(state="normal", text="📄 Upload Roof Report & Generate Material List"))
                    return

                # Show extraction results
                if data.get('extracted_text_length', 0) > 0:
                    root.after(0, lambda: set_status(f"Extracted {data['extracted_text_length']} characters. Vendor: {data.get('vendor', 'unknown')}"))

                # Prompt for missing data (must be done on main thread)
                root.after(0, lambda: continue_processing(data, product))

            except Exception as e:
                error_msg = f"Error processing PDF: {str(e)}"
                root.after(0, lambda: set_status(error_msg))
                root.after(0, lambda: messagebox.showerror("Processing Error", error_msg))
                root.after(0, lambda: generate_btn.config(state="normal", text="📄 Upload Roof Report & Generate Material List"))

        def continue_processing(data, product):
            """Continue processing after data extraction."""
            try:
                # Prompt for missing data
                data = prompt_for_missing(data)

                set_status("Loading parts list...")
                parts = load_parts(product)
                if not parts:
                    set_status("No parts found for product.")
                    generate_btn.config(state="normal", text="📄 Upload Roof Report & Generate Material List")
                    return

                set_status("Calculating materials...")
                results = calculate_materials(data, parts)

                save_path = filedialog.asksaveasfilename(
                    title="Save Material List",
                    defaultextension=".pdf",
                    filetypes=[("PDF", "*.pdf")]
                )
                if not save_path:
                    set_status("Save cancelled.")
                    generate_btn.config(state="normal", text="📄 Upload Roof Report & Generate Material List")
                    return

                set_status("Generating PDF report...")
                pdf = MaterialListPDF()
                pdf.add_page()
                pdf.set_font("Arial", "", 10)
                pdf.cell(0, 10, f"Vendor Detected: {data.get('vendor', 'unknown')}", ln=1)
                pdf.cell(0, 10, f"Product Line: {product}", ln=1)
                pdf.cell(0, 10, f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
                pdf.cell(0, 10, f"Text Extracted: {data.get('extracted_text_length', 0)} characters", ln=1)
                pdf.ln(5)
                pdf.add_table(results)
                pdf.output(save_path)

                set_status("PDF saved successfully!")
                messagebox.showinfo("Success", f"Material list saved to:\n{save_path}")

            except Exception as e:
                set_status("Failed to generate PDF.")
                messagebox.showerror("PDF Error", f"Failed to generate PDF: {e}")
            finally:
                generate_btn.config(state="normal", text="📄 Upload Roof Report & Generate Material List")

        # Start processing in background thread
        thread = threading.Thread(target=process_pdf, daemon=True)
        thread.start()

    # Main action button
    btn_frame = tk.Frame(root)
    btn_frame.pack(pady=20)
    generate_btn = tk.Button(
        btn_frame,
        text="📄 Upload Roof Report & Generate Material List",
        command=generate,
        width=45,
        height=2,
        font=("Arial", 10, "bold"),
        bg="#4CAF50",
        fg="white",
        relief="raised"
    )
    generate_btn.pack()

    # Status label
    status_frame = tk.Frame(root)
    status_frame.pack(pady=(10,15))
    tk.Label(status_frame, textvariable=status_var, fg="blue", font=("Arial", 9)).pack()

    # Footer
    footer_frame = tk.Frame(root)
    footer_frame.pack(side="bottom", pady=5)
    tk.Label(footer_frame, text=f"© 2024 {COMPANY_NAME}", font=("Arial", 8), fg="gray").pack()

    root.mainloop()

if __name__ == "__main__":
    run_gui() 