import pdfplumber
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
from fpdf import FPDF
import pandas as pd
import re
import math
from typing import List, Dict, Any
import threading

# Modern UI Colors and Styling
COLORS = {
    'primary': '#2C3E50',      # Dark blue-gray
    'secondary': '#3498DB',     # Bright blue
    'accent': '#E74C3C',       # Red accent
    'success': '#27AE60',      # Green
    'warning': '#F39C12',      # Orange
    'background': '#ECF0F1',   # Light gray
    'card': '#FFFFFF',         # White
    'text_primary': '#2C3E50', # Dark text
    'text_secondary': '#7F8C8D', # Gray text
    'border': '#BDC3C7'        # Light border
}

CSV_PATH = "parts.csv"
COMPANY_NAME = "Isaiah Industries"
PRODUCTS = ["Country Manor Shake", "Oxford Shingle"]

def load_parts(product_name: str, csv_path: str = CSV_PATH) -> List[Dict[str, Any]]:
    """Load parts for a given product from CSV."""
    import os

    # Try multiple possible locations for the CSV file
    possible_paths = [
        csv_path,  # Original path
        os.path.join(os.path.dirname(__file__), csv_path),  # Same directory as script
        os.path.join(os.getcwd(), csv_path),  # Current working directory
        os.path.join(os.path.dirname(os.path.abspath(__file__)), csv_path),  # Absolute script directory
    ]

    csv_file_found = None
    for path in possible_paths:
        if os.path.exists(path):
            csv_file_found = path
            break

    if not csv_file_found:
        error_msg = f"Parts database not found!\n\nLooked in these locations:\n"
        for path in possible_paths:
            error_msg += f"• {path}\n"
        error_msg += f"\nCurrent directory: {os.getcwd()}"
        messagebox.showerror("Parts Database Error", error_msg)
        return []

    try:
        df = pd.read_csv(csv_file_found)
        if "product" not in df.columns:
            raise ValueError("CSV missing 'product' column.")

        # Filter for the specific product
        product_parts = df[df["product"].str.lower() == product_name.lower()]

        if len(product_parts) == 0:
            available_products = df["product"].unique().tolist()
            error_msg = f"No parts found for product: {product_name}\n\nAvailable products:\n"
            for prod in available_products:
                error_msg += f"• {prod}\n"
            messagebox.showerror("Product Not Found", error_msg)
            return []

        return product_parts.to_dict(orient="records")

    except Exception as e:
        messagebox.showerror("CSV Error", f"Failed to load parts from {csv_file_found}: {e}")
        return []

def advanced_pdf_extraction(path: str, progress_callback=None) -> Dict[str, Any]:
    """Ultra-robust PDF extraction using multiple advanced methods."""
    if progress_callback:
        progress_callback("🔍 Starting advanced PDF analysis...")

    extraction_results = {
        "text_content": "",
        "tables_data": [],
        "metadata": {},
        "objects": [],
        "annotations": [],
        "forms": []
    }

    try:
        with pdfplumber.open(path) as pdf:
            if progress_callback:
                progress_callback(f"📄 Analyzing {len(pdf.pages)} pages with multiple methods...")

            # Extract metadata
            extraction_results["metadata"] = pdf.metadata or {}

            for i, page in enumerate(pdf.pages):
                if progress_callback:
                    progress_callback(f"🔬 Deep analysis of page {i+1}/{len(pdf.pages)}...")

                page_results = extract_page_comprehensive(page, i+1, progress_callback)

                # Combine results
                extraction_results["text_content"] += page_results["text"] + "\n"
                extraction_results["tables_data"].extend(page_results["tables"])
                extraction_results["objects"].extend(page_results["objects"])
                extraction_results["annotations"].extend(page_results["annotations"])
                extraction_results["forms"].extend(page_results["forms"])

    except Exception as e:
        if progress_callback:
            progress_callback(f"❌ PDF reading error: {str(e)}")
        return {"error": str(e)}

    return extraction_results

def extract_page_comprehensive(page, page_num: int, progress_callback=None) -> Dict[str, Any]:
    """Comprehensive extraction from a single page using all available methods."""
    results = {
        "text": "",
        "tables": [],
        "objects": [],
        "annotations": [],
        "forms": []
    }

    # Method 1: Standard text extraction
    try:
        text = page.extract_text() or ""
        results["text"] += text + "\n"
    except:
        pass

    # Method 2: Layout-preserved text extraction
    try:
        layout_text = page.extract_text(layout=True) or ""
        if layout_text and layout_text != results["text"]:
            results["text"] += layout_text + "\n"
    except:
        pass

    # Method 3: Character-level extraction with positioning
    try:
        chars = page.chars
        if chars:
            # Group characters by lines and extract text
            char_text = extract_text_from_chars(chars)
            if char_text and char_text not in results["text"]:
                results["text"] += char_text + "\n"
    except:
        pass

    # Method 4: Word-level extraction
    try:
        words = page.extract_words()
        if words:
            word_text = " ".join([word["text"] for word in words if word.get("text")])
            if word_text and word_text not in results["text"]:
                results["text"] += word_text + "\n"
    except:
        pass

    # Method 5: Table extraction with multiple strategies
    try:
        # Strategy 1: Default table extraction
        tables = page.extract_tables()
        if tables:
            results["tables"].extend(tables)

        # Strategy 2: Table extraction with custom settings
        tables_custom = page.extract_tables(table_settings={
            "vertical_strategy": "lines_strict",
            "horizontal_strategy": "lines_strict",
            "intersection_tolerance": 3
        })
        if tables_custom:
            for table in tables_custom:
                if table not in results["tables"]:
                    results["tables"].append(table)

        # Strategy 3: Find tables by text patterns
        implicit_tables = find_implicit_tables(results["text"])
        results["tables"].extend(implicit_tables)

    except:
        pass

    # Method 6: Extract from different regions
    try:
        regions = [
            (0, 0, page.width, page.height * 0.25),  # Top quarter
            (0, page.height * 0.25, page.width, page.height * 0.5),  # Second quarter
            (0, page.height * 0.5, page.width, page.height * 0.75),  # Third quarter
            (0, page.height * 0.75, page.width, page.height),  # Bottom quarter
            (0, 0, page.width * 0.5, page.height),  # Left half
            (page.width * 0.5, 0, page.width, page.height),  # Right half
        ]

        for bbox in regions:
            try:
                cropped = page.crop(bbox)
                region_text = cropped.extract_text() or ""
                if region_text.strip() and region_text not in results["text"]:
                    results["text"] += f"\n[Region {bbox}]: {region_text}\n"
            except:
                continue
    except:
        pass

    # Method 7: Extract objects and annotations
    try:
        # Get page objects
        if hasattr(page, 'objects'):
            results["objects"] = page.objects

        # Extract annotations
        if hasattr(page, 'annots'):
            for annot in page.annots or []:
                if annot.get('contents'):
                    results["annotations"].append(annot['contents'])
                    results["text"] += f"\n[Annotation]: {annot['contents']}\n"
    except:
        pass

    # Method 8: Form field extraction
    try:
        # Look for form fields
        if hasattr(page, 'form_fields'):
            for field in page.form_fields or []:
                if field.get('value'):
                    results["forms"].append(field)
                    results["text"] += f"\n[Form Field]: {field.get('name', 'Unknown')}: {field['value']}\n"
    except:
        pass

    return results

def extract_text_from_chars(chars):
    """Extract text from character-level data with positioning."""
    if not chars:
        return ""

    # Sort characters by position (top to bottom, left to right)
    sorted_chars = sorted(chars, key=lambda c: (c.get('top', 0), c.get('x0', 0)))

    text_lines = []
    current_line = []
    current_top = None

    for char in sorted_chars:
        char_top = char.get('top', 0)
        char_text = char.get('text', '')

        # If this is a new line (different vertical position)
        if current_top is None or abs(char_top - current_top) > 2:
            if current_line:
                text_lines.append(''.join(current_line))
            current_line = [char_text]
            current_top = char_top
        else:
            current_line.append(char_text)

    # Add the last line
    if current_line:
        text_lines.append(''.join(current_line))

    return '\n'.join(text_lines)

def find_implicit_tables(text: str) -> List[List[List[str]]]:
    """Find table-like structures in text using patterns."""
    tables = []
    lines = text.split('\n')

    # Look for lines that might be table rows
    potential_rows = []
    for line in lines:
        # Check if line has multiple numeric values or consistent separators
        if re.search(r'\d+.*\d+.*\d+', line) or line.count('\t') >= 2 or line.count('  ') >= 3:
            # Split by common separators
            if '\t' in line:
                row = line.split('\t')
            elif '  ' in line:
                row = re.split(r'\s{2,}', line)
            else:
                row = re.split(r'\s+', line)

            if len(row) >= 2:
                potential_rows.append([cell.strip() for cell in row if cell.strip()])

    # Group consecutive rows into tables
    if potential_rows:
        tables.append(potential_rows)

    return tables

def extract_roof_data(path: str, progress_callback=None) -> Dict[str, Any]:
    """Simplified, reliable PDF text extraction focused on getting measurements."""
    if progress_callback:
        progress_callback("📄 Reading PDF text content...")

    all_text = ""

    try:
        with pdfplumber.open(path) as pdf:
            if progress_callback:
                progress_callback(f"📖 Processing {len(pdf.pages)} pages...")

            for i, page in enumerate(pdf.pages):
                if progress_callback:
                    progress_callback(f"Reading page {i+1}/{len(pdf.pages)}...")

                # Method 1: Standard text extraction
                page_text = page.extract_text() or ""
                all_text += page_text + "\n"

                # Method 2: Try layout-preserved extraction if standard didn't work well
                if len(page_text.strip()) < 50:
                    try:
                        layout_text = page.extract_text(layout=True) or ""
                        all_text += layout_text + "\n"
                    except:
                        pass

                # Method 3: Extract tables as text
                try:
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            for row in table:
                                if row:
                                    row_text = " ".join([str(cell) if cell else "" for cell in row])
                                    all_text += row_text + "\n"
                except:
                    pass

    except Exception as e:
        if progress_callback:
            progress_callback(f"❌ PDF reading error: {str(e)}")
        messagebox.showerror("PDF Error", f"Failed to read PDF: {str(e)}")
        return {}

    if progress_callback:
        progress_callback(f"📊 Extracted {len(all_text)} characters from PDF")

    # If no meaningful text found, offer manual input
    if len(all_text.strip()) < 20:
        if progress_callback:
            progress_callback("⚠️ No text found - offering manual input")

        response = messagebox.askyesno(
            "PDF Text Extraction",
            "No readable text found in this PDF.\n\n"
            "This might be an image-based or scanned PDF.\n\n"
            "Would you like to enter measurements manually?"
        )

        if response:
            return {"vendor": "manual_input", "extracted_text_length": 0}
        else:
            return {}

    # Analyze the extracted text for measurements
    return analyze_text_for_measurements(all_text, progress_callback)

def analyze_text_for_measurements(text: str, progress_callback=None) -> Dict[str, Any]:
    """Simple, focused analysis of text to find roof measurements."""
    if progress_callback:
        progress_callback("🔍 Looking for measurements in text...")

    # Detect vendor
    vendor = "unknown"
    vendor_patterns = {
        "EagleView": [r"EagleView", r"Eagle\s*View", r"EAGLEVIEW"],
        "RoofSnap": [r"RoofSnap", r"Roof\s*Snap", r"ROOFSNAP"],
        "Hover": [r"Hover", r"HOVER"],
        "Roofr": [r"Roofr", r"Roof\s*Sketch", r"ROOFR"],
        "GAF": [r"GAF", r"QuickMeasure"],
        "Xactimate": [r"Xactimate", r"XACTIMATE"]
    }

    for vendor_name, patterns in vendor_patterns.items():
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                vendor = vendor_name
                break
        if vendor != "unknown":
            break

    # Simple, reliable measurement patterns
    patterns = {
        "total_sqft": [
            r"Total\s+Area[:=]?\s*([\d,]+\.?\d*)",
            r"Roof\s+Area[:=]?\s*([\d,]+\.?\d*)",
            r"Building\s+Area[:=]?\s*([\d,]+\.?\d*)",
            r"(\d{1,6})\s*(?:sq\.?\s*ft|SF|square\s+feet)",
            r"Area[:=]?\s*([\d,]+)\s*(?:sq\.?\s*ft|SF)"
        ],
        "ridge_length": [
            r"Ridge\s*(?:Length)?[:=]?\s*([\d,]+)",
            r"(\d{1,4})\s*(?:ft|feet)?\s*ridge"
        ],
        "hip_length": [
            r"Hip\s*(?:Length)?[:=]?\s*([\d,]+)",
            r"(\d{1,4})\s*(?:ft|feet)?\s*hip"
        ],
        "valley_length": [
            r"Valley\s*(?:Length)?[:=]?\s*([\d,]+)",
            r"(\d{1,4})\s*(?:ft|feet)?\s*valley"
        ],
        "eaves_length": [
            r"Eave\s*(?:Length)?[:=]?\s*([\d,]+)",
            r"Perimeter[:=]?\s*([\d,]+)",
            r"(\d{1,4})\s*(?:ft|feet)?\s*(?:eave|perimeter)"
        ]
    }

    result = {"vendor": vendor, "extracted_text_length": len(text)}

    # Find measurements
    for field, pats in patterns.items():
        found_value = None

        for pat in pats:
            matches = re.finditer(pat, text, re.IGNORECASE)
            for match in matches:
                try:
                    value_str = match.group(1).replace(",", "")
                    value = int(float(value_str))

                    # Basic validation
                    if field == "total_sqft" and 100 <= value <= 50000:
                        found_value = value
                        break
                    elif field != "total_sqft" and 1 <= value <= 2000:
                        found_value = value
                        break
                except (ValueError, IndexError):
                    continue

            if found_value is not None:
                break

        result[field] = found_value

    if progress_callback:
        found_count = sum(1 for v in result.values() if v is not None and isinstance(v, (int, float)))
        progress_callback(f"✅ Found {found_count} measurements!")

    return result

def analyze_extracted_content(all_text: str, extraction_results: Dict, progress_callback=None) -> Dict[str, Any]:
    """Analyze the extracted content to find roof measurements and vendor information."""
    if progress_callback:
        progress_callback("🔍 Analyzing extracted content for measurements...")

    # Enhanced vendor detection with more patterns
    vendor = "unknown"
    vendor_patterns = {
        "EagleView": [r"EagleView", r"Eagle\s*View", r"EAGLEVIEW", r"Pictometry"],
        "RoofSnap": [r"RoofSnap", r"Roof\s*Snap", r"ROOFSNAP"],
        "Hover": [r"Hover", r"HOVER"],
        "Roofr": [r"Roofr", r"Roof\s*Sketch", r"ROOFR"],
        "GAF": [r"GAF", r"QuickMeasure"],
        "Xactimate": [r"Xactimate", r"XACTIMATE"],
        "CompanyCam": [r"CompanyCam", r"Company\s*Cam"],
        "CAPE": [r"CAPE\s*Analytics", r"CAPE"],
        "Nearmap": [r"Nearmap", r"NEARMAP"],
        "Loveland": [r"Loveland", r"LOVELAND"]
    }

    for vendor_name, patterns in vendor_patterns.items():
        for pattern in patterns:
            if re.search(pattern, all_text, re.IGNORECASE):
                vendor = vendor_name
                break
        if vendor != "unknown":
            break

    # Ultra-comprehensive measurement extraction patterns
    patterns = {
        "total_sqft": [
            # Standard formats
            r"Total\s+(?:Roof\s+)?Area[:=]?\s*([\d,]+\.?\d*)",
            r"Roof\s+(?:Surface\s+)?Area[:=]?\s*([\d,]+\.?\d*)",
            r"Total\s+Square\s+Feet[:=]?\s*([\d,]+\.?\d*)",
            r"Total\s+Sq\.?\s*Ft\.?[:=]?\s*([\d,]+\.?\d*)",
            r"Building\s+Area[:=]?\s*([\d,]+\.?\d*)",
            r"Footprint[:=]?\s*([\d,]+\.?\d*)",
            # Table and numeric formats
            r"(\d{1,6}\.?\d*)\s*sq\.?\s*ft\.?\s*(?:total|roof|area)",
            r"(\d{1,6}\.?\d*)\s*square\s+feet",
            r"(\d{1,6}\.?\d*)\s*SF\s*(?:total|roof)?",
            r"(\d{1,6}\.?\d*)\s*sqft",
            # With units and context
            r"Area[:=]?\s*([\d,]+\.?\d*)\s*(?:sq\.?\s*ft|SF|square\s+feet)",
            r"(?:Total|Roof|Building)[:=]?\s*([\d,]+\.?\d*)\s*(?:sq\.?\s*ft|SF)",
            # Measurement table formats
            r"(?:Area|Total|Roof)\s*[:\|]\s*([\d,]+\.?\d*)",
            r"([\d,]+\.?\d*)\s*(?:sq\.?\s*ft\.?|SF)\s*(?:roof|total|area|building)",
        ],
        "ridge_length": [
            r"Ridge\s*(?:Length|Line)?[:=]?\s*([\d,]+\.?\d*)",
            r"Ridges?[:=]?\s*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)",
            r"(\d{1,5}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:ridge|ridges)",
            r"Ridge\s*[:\|]\s*([\d,]+\.?\d*)",
            r"(?:Peak|Ridge)\s+(?:Length|Line)[:=]?\s*([\d,]+\.?\d*)",
            r"([\d,]+\.?\d*)\s*(?:ft|feet)?\s*(?:of\s+)?ridge",
        ],
        "hip_length": [
            r"Hip\s*(?:Length|Line)?[:=]?\s*([\d,]+\.?\d*)",
            r"Hips?[:=]?\s*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)",
            r"(\d{1,5}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:hip|hips)",
            r"Hip\s*[:\|]\s*([\d,]+\.?\d*)",
            r"([\d,]+\.?\d*)\s*(?:ft|feet)?\s*(?:of\s+)?hip",
        ],
        "valley_length": [
            r"Valley\s*(?:Length|Line)?[:=]?\s*([\d,]+\.?\d*)",
            r"Valleys?[:=]?\s*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)",
            r"(\d{1,5}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:valley|valleys)",
            r"Valley\s*[:\|]\s*([\d,]+\.?\d*)",
            r"([\d,]+\.?\d*)\s*(?:ft|feet)?\s*(?:of\s+)?valley",
        ],
        "eaves_length": [
            r"Eave\s*(?:Length|Line)?[:=]?\s*([\d,]+\.?\d*)",
            r"Eaves?[:=]?\s*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)",
            r"Gutter\s*(?:Length|Line)?[:=]?\s*([\d,]+\.?\d*)",
            r"Perimeter[:=]?\s*([\d,]+\.?\d*)",
            r"Drip\s+Edge[:=]?\s*([\d,]+\.?\d*)",
            r"(\d{1,5}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:eave|eaves|gutter|perimeter)",
            r"(?:Eave|Gutter|Perimeter)\s*[:\|]\s*([\d,]+\.?\d*)",
            r"([\d,]+\.?\d*)\s*(?:ft|feet)?\s*(?:of\s+)?(?:eave|gutter|perimeter)",
        ],
    }

    result = {"vendor": vendor, "extracted_text_length": len(all_text)}

    # Enhanced pattern matching with scoring system
    for field, pats in patterns.items():
        candidates = []

        for pat in pats:
            matches = re.finditer(pat, all_text, re.IGNORECASE)
            for match in matches:
                try:
                    value_str = match.group(1).replace(",", "").replace(" ", "")
                    value = float(value_str)

                    # Score based on context and reasonableness
                    score = calculate_measurement_score(value, field, match.group(0), all_text)

                    if score > 0:
                        candidates.append((value, score, match.group(0)))

                except (ValueError, IndexError, AttributeError):
                    continue

        # Select best candidate
        if candidates:
            best_candidate = max(candidates, key=lambda x: x[1])
            result[field] = int(best_candidate[0])
        else:
            result[field] = None

    # Analyze tables for additional measurements
    tables_data = extraction_results.get("tables_data", [])
    if tables_data:
        table_measurements = extract_from_tables(tables_data, patterns)

        # Fill in missing measurements from tables
        for field, value in table_measurements.items():
            if result.get(field) is None and value is not None:
                result[field] = value

    # Smart inference for missing measurements
    result = infer_missing_measurements(result, all_text)

    if progress_callback:
        found_count = sum(1 for v in result.values() if v is not None and isinstance(v, (int, float)))
        progress_callback(f"✅ Found {found_count} measurements from PDF!")

    return result

def calculate_measurement_score(value: float, field: str, context: str, full_text: str) -> float:
    """Calculate a score for how likely a measurement is correct."""
    score = 0.0

    # Base reasonableness check
    if field == "total_sqft":
        if 100 <= value <= 50000:
            score += 1.0
        elif 50 <= value <= 100000:
            score += 0.5
        else:
            return 0.0
    else:  # Linear measurements
        if 1 <= value <= 2000:
            score += 1.0
        elif 0.1 <= value <= 5000:
            score += 0.5
        else:
            return 0.0

    # Context scoring
    context_lower = context.lower()

    # Positive context indicators
    positive_indicators = {
        "total_sqft": ["total", "roof", "area", "building", "footprint"],
        "ridge_length": ["ridge", "peak", "line"],
        "hip_length": ["hip"],
        "valley_length": ["valley"],
        "eaves_length": ["eave", "gutter", "perimeter", "drip"]
    }

    for indicator in positive_indicators.get(field, []):
        if indicator in context_lower:
            score += 0.3

    # Unit indicators
    if any(unit in context_lower for unit in ["sq ft", "sf", "square feet"]) and field == "total_sqft":
        score += 0.5
    elif any(unit in context_lower for unit in ["ft", "feet", "linear"]) and field != "total_sqft":
        score += 0.5

    # Proximity to field name in full text
    field_name = field.replace("_", " ")
    if field_name in full_text.lower():
        score += 0.2

    return score

def extract_from_tables(tables_data: List, patterns: Dict) -> Dict[str, int]:
    """Extract measurements from table data."""
    measurements = {}

    for table in tables_data:
        for row in table:
            if not row:
                continue

            row_text = " ".join([str(cell) if cell else "" for cell in row])

            for field, pats in patterns.items():
                if field in measurements:
                    continue

                for pat in pats:
                    match = re.search(pat, row_text, re.IGNORECASE)
                    if match:
                        try:
                            value_str = match.group(1).replace(",", "").replace(" ", "")
                            value = float(value_str)

                            score = calculate_measurement_score(value, field, match.group(0), row_text)
                            if score > 0.5:  # Higher threshold for table data
                                measurements[field] = int(value)
                                break
                        except (ValueError, IndexError, AttributeError):
                            continue

    return measurements

def infer_missing_measurements(result: Dict[str, Any], text: str) -> Dict[str, Any]:
    """Use smart inference to estimate missing measurements."""

    # If we have total sqft but missing linear measurements, try to estimate
    total_sqft = result.get("total_sqft")
    if total_sqft and total_sqft > 0:

        # Estimate perimeter/eaves from square footage (rough approximation)
        if result.get("eaves_length") is None:
            # For a square building, perimeter ≈ 4 * sqrt(area)
            estimated_perimeter = int(4 * (total_sqft ** 0.5))
            if 50 <= estimated_perimeter <= 1000:  # Reasonable range
                # Don't auto-assign, but note it for potential use
                pass

        # Look for any numeric values that might be measurements
        numbers = re.findall(r'\b(\d{1,4})\b', text)
        reasonable_numbers = [int(n) for n in numbers if 10 <= int(n) <= 500]

        # If we're missing measurements and have reasonable numbers, suggest them
        missing_fields = [field for field in ["ridge_length", "hip_length", "valley_length", "eaves_length"]
                         if result.get(field) is None]

        if missing_fields and reasonable_numbers:
            # This is just for logging - we won't auto-assign
            pass

    return result

def prompt_for_missing(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prompt user for any missing data."""
    # Fields that should be prompted for
    measurement_fields = ["total_sqft", "ridge_length", "hip_length", "valley_length", "eaves_length"]

    for field in measurement_fields:
        value = data.get(field)
        if value is None or value == 0:
            ans = simpledialog.askinteger("Missing Info", f"Enter {field.replace('_',' ')} (in feet):")
            data[field] = ans if ans else 0
    return data

def calculate_materials(data: Dict[str, Any], parts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Calculate materials based on parts list and extracted data."""
    results = []
    for row in parts:
        src_val = data.get(row.get("source_metric", ""), 0)
        try:
            coverage = float(row.get("coverage", 1))
            waste = float(row.get("waste", 0))
        except (TypeError, ValueError):
            coverage, waste = 1, 0
        unit = row.get("unit", "pieces")
        count = math.ceil((src_val / coverage) * (1 + waste)) if coverage else 0
        results.append({
            "part_number": row.get("part_number", ""),
            "description": row.get("description", ""),
            "unit": unit,
            "count": count
        })
    return results

class MaterialListPDF(FPDF):
    """Professional PDF output for material list with Isaiah Industries branding."""
    def header(self):
        # Company header with professional styling
        self.set_fill_color(44, 62, 80)  # Dark blue background
        self.rect(0, 0, 210, 25, 'F')

        self.set_text_color(255, 255, 255)  # White text
        self.set_font("Arial", "B", 18)
        self.cell(0, 15, f"{COMPANY_NAME}", 0, 1, "C")

        self.set_text_color(200, 200, 200)  # Light gray
        self.set_font("Arial", "", 12)
        self.cell(0, 8, "Professional Roof Material Analysis", 0, 1, "C")

        self.ln(10)

        # Reset colors for body
        self.set_text_color(0, 0, 0)

    def footer(self):
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.set_text_color(128, 128, 128)
        self.cell(0, 10, f'Generated by {COMPANY_NAME} - Page {self.page_no()}', 0, 0, 'C')

    def add_table(self, data: List[Dict[str, Any]]):
        # Table header with professional styling
        self.set_fill_color(52, 152, 219)  # Blue header
        self.set_text_color(255, 255, 255)  # White text
        self.set_font("Arial", "B", 11)

        col_width = [45, 80, 25, 25]
        headers = ["Part Number", "Description", "Unit", "Quantity"]

        for i, h in enumerate(headers):
            self.cell(col_width[i], 12, h, border=1, fill=True, align='C')
        self.ln()

        # Table rows with alternating colors
        self.set_text_color(0, 0, 0)  # Black text
        self.set_font("Arial", "", 10)

        for i, row in enumerate(data):
            # Alternate row colors
            if i % 2 == 0:
                self.set_fill_color(248, 249, 250)  # Light gray
            else:
                self.set_fill_color(255, 255, 255)  # White

            self.cell(col_width[0], 10, str(row.get("part_number", "")), border=1, fill=True)
            self.cell(col_width[1], 10, str(row.get("description", ""))[:35], border=1, fill=True)  # Truncate long descriptions
            self.cell(col_width[2], 10, str(row.get("unit", "")), border=1, fill=True, align='C')
            self.cell(col_width[3], 10, str(row.get("count", "")), border=1, fill=True, align='C')
            self.ln()

        # Add summary section
        self.ln(5)
        self.set_font("Arial", "B", 10)
        self.cell(0, 8, f"Total Items: {len(data)}", 0, 1)

        # Add note
        self.ln(5)
        self.set_font("Arial", "I", 9)
        self.set_text_color(100, 100, 100)
        self.cell(0, 6, "Note: Quantities include recommended waste factors. Verify measurements before ordering.", 0, 1)
        self.cell(0, 6, "For technical support, contact Isaiah Industries customer service.", 0, 1)

def create_modern_button(parent, text, command, bg_color=COLORS['secondary'], fg_color='white', width=None, height=None):
    """Create a modern-styled button."""
    btn = tk.Button(
        parent,
        text=text,
        command=command,
        bg=bg_color,
        fg=fg_color,
        font=('Segoe UI', 10, 'bold'),
        relief='flat',
        borderwidth=0,
        cursor='hand2',
        padx=20,
        pady=10
    )
    if width:
        btn.config(width=width)
    if height:
        btn.config(height=height)

    # Hover effects
    def on_enter(_):
        btn.config(bg=darken_color(bg_color))
    def on_leave(_):
        btn.config(bg=bg_color)

    btn.bind("<Enter>", on_enter)
    btn.bind("<Leave>", on_leave)

    return btn

def darken_color(hex_color, factor=0.8):
    """Darken a hex color by a factor."""
    hex_color = hex_color.lstrip('#')
    rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    darkened = tuple(int(c * factor) for c in rgb)
    return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

def create_card_frame(parent, bg_color=COLORS['card']):
    """Create a modern card-style frame."""
    frame = tk.Frame(parent, bg=bg_color, relief='flat', bd=1)
    frame.config(highlightbackground=COLORS['border'], highlightthickness=1)
    return frame

def run_gui():
    """Main GUI loop with modern Isaiah Industries design."""
    root = tk.Tk()
    root.title(f"{COMPANY_NAME} - Roof Material Analysis Tool")
    root.geometry("800x650")
    root.configure(bg=COLORS['background'])
    root.resizable(True, False)

    # Configure modern font
    default_font = ('Segoe UI', 10)
    root.option_add('*Font', default_font)

    # Main container with padding
    main_container = tk.Frame(root, bg=COLORS['background'])
    main_container.pack(fill='both', expand=True, padx=30, pady=20)

    # Header section
    header_card = create_card_frame(main_container)
    header_card.pack(fill='x', pady=(0, 20))

    header_content = tk.Frame(header_card, bg=COLORS['card'])
    header_content.pack(fill='x', padx=30, pady=25)

    # Company logo/title
    company_label = tk.Label(
        header_content,
        text=f"{COMPANY_NAME}",
        font=('Segoe UI', 24, 'bold'),
        fg=COLORS['primary'],
        bg=COLORS['card']
    )
    company_label.pack()

    subtitle_label = tk.Label(
        header_content,
        text="Professional Roof Material Analysis Tool",
        font=('Segoe UI', 12),
        fg=COLORS['text_secondary'],
        bg=COLORS['card']
    )
    subtitle_label.pack(pady=(5, 0))

    # Product selection section
    product_card = create_card_frame(main_container)
    product_card.pack(fill='x', pady=(0, 20))

    product_content = tk.Frame(product_card, bg=COLORS['card'])
    product_content.pack(fill='x', padx=30, pady=20)

    product_label = tk.Label(
        product_content,
        text="Select Product Line:",
        font=('Segoe UI', 12, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    )
    product_label.pack(anchor='w', pady=(0, 10))

    # Modern combobox styling
    style = ttk.Style()
    style.theme_use('clam')
    style.configure('Modern.TCombobox',
                   fieldbackground=COLORS['background'],
                   background=COLORS['card'],
                   borderwidth=1,
                   relief='flat')

    prod_var = tk.StringVar(value=PRODUCTS[0])
    combo = ttk.Combobox(
        product_content,
        textvariable=prod_var,
        values=PRODUCTS,
        state="readonly",
        style='Modern.TCombobox',
        font=('Segoe UI', 11),
        width=40
    )
    combo.pack(anchor='w')

    # Status section
    status_card = create_card_frame(main_container)
    status_card.pack(fill='x', pady=(0, 20))

    status_content = tk.Frame(status_card, bg=COLORS['card'])
    status_content.pack(fill='x', padx=30, pady=15)

    status_var = tk.StringVar(value="Ready to process roof reports")
    status_label = tk.Label(
        status_content,
        textvariable=status_var,
        font=('Segoe UI', 10),
        fg=COLORS['text_secondary'],
        bg=COLORS['card']
    )
    status_label.pack()

    def set_status(msg: str, color=COLORS['text_secondary']):
        status_var.set(msg)
        status_label.config(fg=color)
        root.update_idletasks()

    def generate():
        product = prod_var.get()
        path = filedialog.askopenfilename(
            title="Select Roof Report PDF",
            filetypes=[("PDF Files", "*.pdf")]
        )
        if not path:
            set_status("No PDF selected.")
            return

        # Update button state
        generate_btn.config(state="disabled", text="🔄 Processing...", bg=COLORS['warning'])
        set_status("Starting PDF analysis...", COLORS['secondary'])

        def process_pdf():
            """Process PDF in background thread to prevent GUI freezing."""
            try:
                # Extract data with progress updates
                def progress_callback(msg):
                    root.after(0, lambda: set_status(msg, COLORS['secondary']))

                data = extract_roof_data(path, progress_callback=progress_callback)

                if not data:
                    root.after(0, lambda: set_status("❌ Failed to extract roof data.", COLORS['accent']))
                    root.after(0, lambda: reset_button())
                    return

                # Show extraction results
                if data.get('extracted_text_length', 0) > 0:
                    success_msg = f"✅ Extracted {data['extracted_text_length']} characters. Vendor: {data.get('vendor', 'unknown')}"
                    root.after(0, lambda: set_status(success_msg, COLORS['success']))

                # Prompt for missing data (must be done on main thread)
                root.after(0, lambda: continue_processing(data, product))

            except Exception as e:
                error_msg = f"❌ Error processing PDF: {str(e)}"
                root.after(0, lambda: set_status(error_msg, COLORS['accent']))
                root.after(0, lambda: messagebox.showerror("Processing Error", error_msg))
                root.after(0, lambda: reset_button())

        def reset_button():
            """Reset button to original state."""
            generate_btn.config(
                state="normal",
                text="📄 Analyze Roof Report",
                bg=COLORS['secondary']
            )

        def continue_processing(data, product):
            """Continue processing after data extraction."""
            try:
                # Prompt for missing data
                set_status("📝 Please enter any missing measurements...", COLORS['warning'])
                data = prompt_for_missing(data)

                set_status("📋 Loading parts database...", COLORS['secondary'])
                parts = load_parts(product)
                if not parts:
                    set_status("❌ No parts found for selected product.", COLORS['accent'])
                    reset_button()
                    return

                set_status("🔢 Calculating material requirements...", COLORS['secondary'])
                results = calculate_materials(data, parts)

                save_path = filedialog.asksaveasfilename(
                    title="Save Material List - Isaiah Industries",
                    defaultextension=".pdf",
                    filetypes=[("PDF", "*.pdf")],
                    initialname=f"Material_List_{product.replace(' ', '_')}"
                )
                if not save_path:
                    set_status("💾 Save cancelled by user.", COLORS['warning'])
                    reset_button()
                    return

                set_status("📄 Generating professional PDF report...", COLORS['secondary'])
                pdf = MaterialListPDF()
                pdf.add_page()
                pdf.set_font("Arial", "", 10)
                pdf.cell(0, 10, f"Vendor Detected: {data.get('vendor', 'unknown')}", ln=1)
                pdf.cell(0, 10, f"Product Line: {product}", ln=1)
                pdf.cell(0, 10, f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
                pdf.cell(0, 10, f"Text Extracted: {data.get('extracted_text_length', 0)} characters", ln=1)
                pdf.ln(5)
                pdf.add_table(results)
                pdf.output(save_path)

                set_status("✅ Material list generated successfully!", COLORS['success'])
                messagebox.showinfo("Success - Isaiah Industries",
                                  f"Professional material list saved to:\n{save_path}\n\nReady for contractor use!")

            except Exception as e:
                set_status("❌ Failed to generate PDF report.", COLORS['accent'])
                messagebox.showerror("PDF Generation Error", f"Failed to generate PDF: {e}")
            finally:
                reset_button()

        # Start processing in background thread
        thread = threading.Thread(target=process_pdf, daemon=True)
        thread.start()

    # Action section - Make this much more prominent
    action_card = create_card_frame(main_container)
    action_card.pack(fill='x', pady=(0, 20))

    action_content = tk.Frame(action_card, bg=COLORS['card'])
    action_content.pack(fill='x', padx=40, pady=40)

    # Large, prominent main action button with gradient-like effect
    button_frame = tk.Frame(action_content, bg=COLORS['card'])
    button_frame.pack(pady=20)

    # Add a subtle shadow effect
    shadow_btn = tk.Button(
        button_frame,
        text="📄 UPLOAD & ANALYZE ROOF REPORT",
        font=(default_font[0], 16, 'bold'),
        bg=darken_color(COLORS['secondary'], 0.7),
        fg=darken_color(COLORS['secondary'], 0.7),
        relief='flat',
        borderwidth=0,
        padx=52,
        pady=27,
        width=40,
        height=4,
        state='disabled'
    )
    shadow_btn.pack()

    # Main button on top of shadow
    generate_btn = tk.Button(
        button_frame,
        text="📄 UPLOAD & ANALYZE ROOF REPORT",
        command=generate,
        font=(default_font[0], 16, 'bold'),
        bg=COLORS['secondary'],
        fg='white',
        relief='flat',
        borderwidth=0,
        cursor='hand2',
        padx=50,
        pady=25,
        width=40,
        height=4
    )
    generate_btn.place(in_=shadow_btn, x=-2, y=-2)

    # Enhanced hover effects for the large button
    def on_enter_large(_):
        generate_btn.config(
            bg=darken_color(COLORS['secondary']),
            font=(default_font[0], 17, 'bold'),
            relief='raised',
            borderwidth=2
        )
    def on_leave_large(_):
        generate_btn.config(
            bg=COLORS['secondary'],
            font=(default_font[0], 16, 'bold'),
            relief='flat',
            borderwidth=0
        )

    generate_btn.bind("<Enter>", on_enter_large)
    generate_btn.bind("<Leave>", on_leave_large)

    # Secondary action - drag and drop hint
    drag_drop_frame = tk.Frame(action_content, bg=COLORS['card'])
    drag_drop_frame.pack(pady=(15, 0))

    tk.Label(
        drag_drop_frame,
        text="💡 Pro Tip: Click the big button above to select your PDF file",
        font=(default_font[0], 11, 'italic'),
        fg=COLORS['warning'],
        bg=COLORS['card']
    ).pack()

    # Help text with better formatting
    help_frame = tk.Frame(action_content, bg=COLORS['card'])
    help_frame.pack(pady=(20, 0))

    tk.Label(
        help_frame,
        text="✅ Supported PDF Formats:",
        font=(default_font[0], 12, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    ).pack()

    tk.Label(
        help_frame,
        text="EagleView • RoofSnap • Hover • Roofr • GAF • Xactimate • Any PDF with roof measurements",
        font=(default_font[0], 11),
        fg=COLORS['text_secondary'],
        bg=COLORS['card'],
        wraplength=600
    ).pack(pady=(8, 0))

    # Footer section
    footer_frame = tk.Frame(main_container, bg=COLORS['background'])
    footer_frame.pack(side="bottom", fill='x', pady=(20, 0))

    footer_content = tk.Frame(footer_frame, bg=COLORS['background'])
    footer_content.pack()

    # Company info
    tk.Label(
        footer_content,
        text=f"© 2024 {COMPANY_NAME} - Professional Roofing Solutions",
        font=('Segoe UI', 9),
        fg=COLORS['text_secondary'],
        bg=COLORS['background']
    ).pack()

    # Version info
    tk.Label(
        footer_content,
        text="Version 2.0 - Commercial Grade Tool",
        font=('Segoe UI', 8),
        fg=COLORS['text_secondary'],
        bg=COLORS['background']
    ).pack()

    root.mainloop()

if __name__ == "__main__":
    run_gui() 