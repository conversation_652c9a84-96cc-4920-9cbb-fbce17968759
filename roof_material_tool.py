import pdfplumber
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
from fpdf import FPDF
import pandas as pd
import re
import math
import os
from typing import List, Dict, Any, Optional

CSV_PATH = "parts.csv"
PRODUCTS = ["Country Manor Shake", "Oxford Shingle"]

def load_parts(product_name: str, csv_path: str = CSV_PATH) -> List[Dict[str, Any]]:
    """Load parts for a given product from CSV."""
    try:
        df = pd.read_csv(csv_path)
        if "product" not in df.columns:
            raise ValueError("CSV missing 'product' column.")
        return df[df["product"].str.lower() == product_name.lower()].to_dict(orient="records")
    except Exception as e:
        messagebox.showerror("CSV Error", f"Failed to load parts: {e}")
        return []

def extract_roof_data(path: str) -> Dict[str, Any]:
    """Detect vendor and extract roof data from PDF."""
    try:
        with pdfplumber.open(path) as pdf:
            text_all = "".join([page.extract_text() or "" for page in pdf.pages])
    except Exception as e:
        messagebox.showerror("PDF Error", f"Failed to read PDF: {e}")
        return {}

    vendor = "unknown"
    if "EagleView" in text_all: vendor = "EagleView"
    elif "RoofSnap" in text_all: vendor = "RoofSnap"
    elif "Hover" in text_all: vendor = "Hover"
    elif "Roofr" in text_all or "Roof Sketch" in text_all: vendor = "Roofr"

    patterns = {
        "total_sqft": [r"Total\s+Area[:=]?\s*([\d,]+)", r"Roof Area[:=]?\s*([\d,]+)"],
        "ridge_length": [r"Ridges?[:=]?\s*([\d,]+)\s*ft", r"Ridge\s+Length[:=]?\s*([\d,]+)"],
        "hip_length": [r"Hips?[:=]?\s*([\d,]+)\s*ft"],
        "valley_length": [r"Valleys?[:=]?\s*([\d,]+)\s*ft"],
        "eaves_length": [r"Eaves?[:=]?\s*([\d,]+)\s*ft"],
    }

    result = {"vendor": vendor}
    for field, pats in patterns.items():
        for pat in pats:
            match = re.search(pat, text_all, re.IGNORECASE)
            if match:
                result[field] = int(match.group(1).replace(",", ""))
                break
        if field not in result:
            result[field] = None
    return result

def prompt_for_missing(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prompt user for any missing data."""
    for k, v in data.items():
        if k != "vendor" and (v is None or v == 0):
            ans = simpledialog.askinteger("Missing Info", f"Enter {k.replace('_',' ')} (in feet):")
            data[k] = ans if ans else 0
    return data

def calculate_materials(data: Dict[str, Any], parts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Calculate materials based on parts list and extracted data."""
    results = []
    for row in parts:
        src_val = data.get(row.get("source_metric", ""), 0)
        try:
            coverage = float(row.get("coverage", 1))
            waste = float(row.get("waste", 0))
        except (TypeError, ValueError):
            coverage, waste = 1, 0
        unit = row.get("unit", "pieces")
        count = math.ceil((src_val / coverage) * (1 + waste)) if coverage else 0
        results.append({
            "part_number": row.get("part_number", ""),
            "description": row.get("description", ""),
            "unit": unit,
            "count": count
        })
    return results

class MaterialListPDF(FPDF):
    """PDF output for material list."""
    def header(self):
        self.set_font("Arial", "B", 14)
        self.cell(0, 10, "Roof Material List", 0, 1, "C")
        self.ln(5)

    def add_table(self, data: List[Dict[str, Any]]):
        self.set_font("Arial", "", 10)
        col_width = [40, 60, 30, 20]
        headers = ["Part #", "Description", "Unit", "Count"]
        for i, h in enumerate(headers):
            self.cell(col_width[i], 8, h, border=1)
        self.ln()
        for row in data:
            self.cell(col_width[0], 8, str(row.get("part_number", "")), border=1)
            self.cell(col_width[1], 8, str(row.get("description", "")), border=1)
            self.cell(col_width[2], 8, str(row.get("unit", "")), border=1)
            self.cell(col_width[3], 8, str(row.get("count", "")), border=1)
            self.ln()

def run_gui():
    """Main GUI loop."""
    root = tk.Tk()
    root.title("Roof Material Tool v11")
    root.geometry("480x220")

    tk.Label(root, text="Select Product Line:").pack(pady=(10,0))
    prod_var = tk.StringVar(value=PRODUCTS[0])
    combo = ttk.Combobox(root, textvariable=prod_var, values=PRODUCTS, state="readonly")
    combo.pack(pady=5)

    status_var = tk.StringVar(value="")

    def set_status(msg: str):
        status_var.set(msg)
        root.update_idletasks()

    def generate():
        product = prod_var.get()
        path = filedialog.askopenfilename(filetypes=[("PDF Files", "*.pdf")])
        if not path:
            set_status("No PDF selected.")
            return
        set_status("Extracting roof data...")
        data = extract_roof_data(path)
        if not data:
            set_status("Failed to extract roof data.")
            return
        data = prompt_for_missing(data)
        set_status("Loading parts list...")
        parts = load_parts(product)
        if not parts:
            set_status("No parts found for product.")
            return
        set_status("Calculating materials...")
        results = calculate_materials(data, parts)
        save_path = filedialog.asksaveasfilename(defaultextension=".pdf", filetypes=[("PDF", "*.pdf")])
        if not save_path:
            set_status("Save cancelled.")
            return
        set_status("Generating PDF...")
        try:
            pdf = MaterialListPDF()
            pdf.add_page()
            pdf.set_font("Arial", "", 10)
            pdf.cell(0, 10, f"Vendor Detected: {data.get('vendor', 'unknown')}", ln=1)
            pdf.cell(0, 10, f"Product: {product}", ln=1)
            pdf.ln(5)
            pdf.add_table(results)
            pdf.output(save_path)
            set_status(f"PDF saved to: {save_path}")
            messagebox.showinfo("Saved", f"PDF saved to: {save_path}")
        except Exception as e:
            set_status("Failed to generate PDF.")
            messagebox.showerror("PDF Error", f"Failed to generate PDF: {e}")

    tk.Button(root, text="Upload Roof Report and Generate List", command=generate, width=40).pack(pady=30)
    tk.Label(root, textvariable=status_var, fg="blue").pack(pady=(0,10))
    root.mainloop()

if __name__ == "__main__":
    run_gui() 