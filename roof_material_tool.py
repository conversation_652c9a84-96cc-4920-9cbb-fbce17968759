import pdfplumber
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
from fpdf import FPDF
import pandas as pd
import re
import math
from typing import List, Dict, Any

# Modern UI Colors and Styling
COLORS = {
    'primary': '#2C3E50',      # Dark blue-gray
    'secondary': '#3498DB',     # Bright blue
    'accent': '#E74C3C',       # Red accent
    'success': '#27AE60',      # Green
    'warning': '#F39C12',      # Orange
    'background': '#ECF0F1',   # Light gray
    'card': '#FFFFFF',         # White
    'text_primary': '#2C3E50', # Dark text
    'text_secondary': '#7F8C8D', # Gray text
    'border': '#BDC3C7'        # Light border
}

CSV_PATH = "parts.csv"
COMPANY_NAME = "Isaiah Industries"
PRODUCTS = ["Country Manor Shake", "Oxford Shingle"]

def load_parts(product_name: str, csv_path: str = CSV_PATH) -> List[Dict[str, Any]]:
    """Load parts for a given product from CSV."""
    import os
    
    # Try multiple possible locations for the CSV file
    possible_paths = [
        csv_path,  # Original path
        os.path.join(os.path.dirname(__file__), csv_path),  # Same directory as script
        os.path.join(os.getcwd(), csv_path),  # Current working directory
        os.path.join(os.path.dirname(os.path.abspath(__file__)), csv_path),  # Absolute script directory
    ]
    
    csv_file_found = None
    for path in possible_paths:
        if os.path.exists(path):
            csv_file_found = path
            break
    
    if not csv_file_found:
        error_msg = f"Parts database not found!\n\nLooked in these locations:\n"
        for path in possible_paths:
            error_msg += f"• {path}\n"
        error_msg += f"\nCurrent directory: {os.getcwd()}"
        messagebox.showerror("Parts Database Error", error_msg)
        return []
    
    try:
        df = pd.read_csv(csv_file_found)
        if "product" not in df.columns:
            raise ValueError("CSV missing 'product' column.")
        
        # Filter for the specific product
        product_parts = df[df["product"].str.lower() == product_name.lower()]
        
        if len(product_parts) == 0:
            available_products = df["product"].unique().tolist()
            error_msg = f"No parts found for product: {product_name}\n\nAvailable products:\n"
            for prod in available_products:
                error_msg += f"• {prod}\n"
            messagebox.showerror("Product Not Found", error_msg)
            return []
        
        return product_parts.to_dict(orient="records")
        
    except Exception as e:
        messagebox.showerror("CSV Error", f"Failed to load parts from {csv_file_found}: {e}")
        return []

def extract_roof_data(path: str, progress_callback=None) -> Dict[str, Any]:
    """Simple, reliable PDF text extraction that actually works."""
    if progress_callback:
        progress_callback("📄 Reading PDF content...")
    
    all_text = ""
    extraction_methods = []
    
    try:
        with pdfplumber.open(path) as pdf:
            if progress_callback:
                progress_callback(f"📖 Processing {len(pdf.pages)} pages...")
            
            for i, page in enumerate(pdf.pages):
                if progress_callback:
                    progress_callback(f"Reading page {i+1}/{len(pdf.pages)}...")
                
                page_text = ""
                
                # Method 1: Standard text extraction
                try:
                    standard_text = page.extract_text() or ""
                    if standard_text.strip():
                        page_text += standard_text + "\n"
                        extraction_methods.append("standard")
                except Exception as e:
                    print(f"Standard extraction failed: {e}")
                
                # Method 2: Layout-preserved extraction (if standard didn't work well)
                if len(page_text.strip()) < 100:
                    try:
                        layout_text = page.extract_text(layout=True) or ""
                        if layout_text.strip():
                            page_text += layout_text + "\n"
                            extraction_methods.append("layout")
                    except Exception as e:
                        print(f"Layout extraction failed: {e}")
                
                # Method 3: Table extraction
                try:
                    tables = page.extract_tables()
                    for table in tables:
                        if table:
                            extraction_methods.append("tables")
                            for row in table:
                                if row:
                                    row_text = " ".join([str(cell) if cell else "" for cell in row])
                                    page_text += row_text + "\n"
                except Exception as e:
                    print(f"Table extraction failed: {e}")
                
                all_text += page_text
                    
    except Exception as e:
        if progress_callback:
            progress_callback(f"❌ PDF reading error: {str(e)}")
        messagebox.showerror("PDF Error", f"Failed to read PDF: {str(e)}")
        return {}
    
    # Analyze what we found
    methods_used = list(set(extraction_methods))
    all_numbers = re.findall(r'\d+\.?\d*', all_text)
    meaningful_numbers = [n for n in all_numbers if len(n) >= 2 and float(n) >= 10]
    
    measurement_keywords = ['area', 'roof', 'ridge', 'hip', 'valley', 'eave', 'perimeter', 'sq', 'ft', 'feet', 'total', 'length']
    has_keywords = any(keyword in all_text.lower() for keyword in measurement_keywords)
    
    if progress_callback:
        progress_callback(f"📊 Extracted {len(all_text)} characters using: {', '.join(methods_used)}")
        progress_callback(f"🔍 Found {len(all_numbers)} numbers, {len(meaningful_numbers)} meaningful, keywords: {has_keywords}")
    
    # Show detailed results to user if no meaningful numbers found
    if len(meaningful_numbers) == 0:
        sample_text = all_text[:500] + "..." if len(all_text) > 500 else all_text
        
        response = messagebox.askyesno(
            "PDF Analysis - Need Your Help", 
            f"Extraction Results:\n"
            f"• Text extracted: {len(all_text)} characters\n"
            f"• All numbers found: {len(all_numbers)}\n"
            f"• Meaningful numbers (≥10): {len(meaningful_numbers)}\n"
            f"• Methods used: {', '.join(methods_used) if methods_used else 'basic'}\n"
            f"• Has roof keywords: {has_keywords}\n\n"
            f"Sample text:\n{sample_text}\n\n"
            f"The PDF has text but no clear measurements were detected.\n"
            f"Would you like to try manual measurement entry?"
        )
        
        if response:
            return {"vendor": "manual_input", "extracted_text_length": len(all_text)}
        else:
            return {}
    
    # Analyze the text for measurements
    return analyze_text_for_measurements(all_text, progress_callback)

def analyze_text_for_measurements(text: str, progress_callback=None) -> Dict[str, Any]:
    """Analyze text to find roof measurements using aggressive pattern matching."""
    if progress_callback:
        progress_callback("🔍 Analyzing text for measurements...")

    # Detect vendor
    vendor = "unknown"
    vendor_patterns = {
        "EagleView": [r"EagleView", r"Eagle\s*View", r"EAGLEVIEW"],
        "RoofSnap": [r"RoofSnap", r"Roof\s*Snap", r"ROOFSNAP"],
        "Hover": [r"Hover", r"HOVER"],
        "Roofr": [r"Roofr", r"Roof\s*Sketch", r"ROOFR"],
        "GAF": [r"GAF", r"QuickMeasure"],
        "Xactimate": [r"Xactimate", r"XACTIMATE"]
    }
    
    for vendor_name, patterns in vendor_patterns.items():
        for pattern in patterns:
            if re.search(pattern, text, re.IGNORECASE):
                vendor = vendor_name
                break
        if vendor != "unknown":
            break

    # Ultra-aggressive measurement patterns
    patterns = {
        "total_sqft": [
            # Standard formats
            r"Total\s*(?:Roof\s*)?Area[:=\s]*([\d,]+\.?\d*)",
            r"Roof\s*(?:Surface\s*)?Area[:=\s]*([\d,]+\.?\d*)",
            r"Building\s*Area[:=\s]*([\d,]+\.?\d*)",
            r"(?:Total|Roof|Building)\s*[:=\s]*([\d,]+\.?\d*)\s*(?:sq\.?\s*ft|SF|square\s*feet)",
            r"(\d{3,6}\.?\d*)\s*(?:sq\.?\s*ft|SF|square\s*feet)",
            r"Area[:=\s]*([\d,]+\.?\d*)\s*(?:sq\.?\s*ft|SF)",
            # Numbers near area keywords
            r"(?:area|roof|building|total).*?(\d{3,6}\.?\d*)",
            r"(\d{3,6}\.?\d*).*?(?:area|roof|building|total)",
            # Just large numbers that could be square footage
            r"\b(\d{4,6})\b(?=.*(?:sq|ft|area|roof))",
        ],
        "ridge_length": [
            r"Ridge\s*(?:Length|Line)?[:=\s]*([\d,]+\.?\d*)",
            r"(?:Ridge|Peak)\s*[:=\s]*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)?",
            r"(\d{1,4}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:ridge|ridges|peak)",
            r"(?:ridge|peak).*?(\d{1,4}\.?\d*)",
            r"(\d{1,4}\.?\d*).*?(?:ridge|peak)",
        ],
        "hip_length": [
            r"Hip\s*(?:Length|Line)?[:=\s]*([\d,]+\.?\d*)",
            r"Hip\s*[:=\s]*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)?",
            r"(\d{1,4}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:hip|hips)",
            r"(?:hip|hips).*?(\d{1,4}\.?\d*)",
            r"(\d{1,4}\.?\d*).*?(?:hip|hips)",
        ],
        "valley_length": [
            r"Valley\s*(?:Length|Line)?[:=\s]*([\d,]+\.?\d*)",
            r"Valley\s*[:=\s]*([\d,]+\.?\d*)\s*(?:ft|feet|linear|lin)?",
            r"(\d{1,4}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:valley|valleys)",
            r"(?:valley|valleys).*?(\d{1,4}\.?\d*)",
            r"(\d{1,4}\.?\d*).*?(?:valley|valleys)",
        ],
        "eaves_length": [
            r"(?:Eave|Eaves)\s*(?:Length|Line)?[:=\s]*([\d,]+\.?\d*)",
            r"(?:Gutter|Gutters)\s*(?:Length|Line)?[:=\s]*([\d,]+\.?\d*)",
            r"Perimeter[:=\s]*([\d,]+\.?\d*)",
            r"(?:Drip\s*Edge|Edge)[:=\s]*([\d,]+\.?\d*)",
            r"(\d{1,4}\.?\d*)\s*(?:ft|feet|lin)?\s*(?:eave|eaves|gutter|perimeter|edge)",
            r"(?:eave|eaves|gutter|perimeter|edge).*?(\d{1,4}\.?\d*)",
            r"(\d{1,4}\.?\d*).*?(?:eave|eaves|gutter|perimeter|edge)",
        ]
    }

    result = {"vendor": vendor, "extracted_text_length": len(text)}
    
    # Find measurements with scoring
    for field, pats in patterns.items():
        candidates = []
        
        for pat in pats:
            matches = re.finditer(pat, text, re.IGNORECASE)
            for match in matches:
                try:
                    value_str = match.group(1).replace(",", "").replace(" ", "")
                    value = float(value_str)
                    
                    # Score this candidate
                    score = 0
                    context = match.group(0).lower()
                    
                    # Basic range validation
                    if field == "total_sqft" and 100 <= value <= 50000:
                        score += 10
                    elif field != "total_sqft" and 1 <= value <= 2000:
                        score += 10
                    else:
                        continue  # Skip if out of reasonable range
                    
                    # Context scoring
                    if field == "total_sqft":
                        if any(word in context for word in ["total", "roof", "area", "building"]):
                            score += 5
                        if any(word in context for word in ["sq", "sf", "square"]):
                            score += 3
                    else:
                        field_name = field.replace("_", " ").replace("length", "")
                        if field_name.strip() in context:
                            score += 5
                        if any(word in context for word in ["ft", "feet", "linear"]):
                            score += 3
                    
                    candidates.append((int(value), score, context))
                    
                except (ValueError, IndexError):
                    continue
        
        # Select best candidate
        if candidates:
            best_candidate = max(candidates, key=lambda x: x[1])
            if best_candidate[1] >= 10:  # Minimum score threshold
                result[field] = best_candidate[0]
            else:
                result[field] = None
        else:
            result[field] = None

    # If no patterns matched, try aggressive number guessing
    if all(result.get(field) is None for field in patterns.keys()):
        if progress_callback:
            progress_callback("🔍 No pattern matches - trying number guessing...")
        
        # Find all numbers and guess what they might be
        all_numbers = re.findall(r'\b(\d{2,6})\b', text)
        numbers = [int(n) for n in all_numbers if int(n) >= 50]
        
        if numbers:
            numbers.sort(reverse=True)
            
            # Guess area (largest number in reasonable range)
            for num in numbers:
                if 500 <= num <= 10000 and result["total_sqft"] is None:
                    result["total_sqft"] = num
                    break
            
            # Guess linear measurements
            linear_candidates = [n for n in numbers if 20 <= n <= 500]
            linear_fields = ["ridge_length", "hip_length", "valley_length", "eaves_length"]
            
            for i, field in enumerate(linear_fields):
                if i < len(linear_candidates) and result[field] is None:
                    result[field] = linear_candidates[i]

    if progress_callback:
        found_count = sum(1 for v in result.values() if v is not None and isinstance(v, (int, float)))
        progress_callback(f"✅ Found {found_count} measurements!")
    
    return result

def calculate_materials(data: Dict[str, Any], parts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Calculate material quantities based on measurements and parts list."""
    results = []

    for part in parts:
        source_metric = part.get("source_metric")
        coverage = part.get("coverage", 1)
        waste_factor = part.get("waste", 0.0)

        if source_metric in data and data[source_metric] is not None:
            measurement = data[source_metric]

            # Calculate base quantity
            base_quantity = measurement / coverage

            # Add waste factor
            final_quantity = base_quantity * (1 + waste_factor)

            # Round up to nearest whole number
            final_count = math.ceil(final_quantity)

            results.append({
                "part_number": part.get("part_number", ""),
                "description": part.get("description", ""),
                "unit": part.get("unit", ""),
                "count": final_count,
                "source_measurement": f"{measurement} from {source_metric}",
                "calculation": f"{measurement} ÷ {coverage} × {1+waste_factor:.2f} = {final_count}"
            })

    return results

class MaterialListPDF(FPDF):
    """Professional PDF output for material list with Isaiah Industries branding."""
    def __init__(self):
        super().__init__()
        self.set_auto_page_break(auto=True, margin=15)

    def header(self):
        try:
            # Company header with professional styling
            self.set_fill_color(44, 62, 80)  # Dark blue background
            self.rect(0, 0, 210, 25, 'F')

            self.set_text_color(255, 255, 255)  # White text
            self.set_font("Arial", "B", 18)
            self.cell(0, 15, COMPANY_NAME, 0, 1, "C")

            self.set_text_color(200, 200, 200)  # Light gray
            self.set_font("Arial", "", 12)
            self.cell(0, 8, "Professional Roof Material Analysis", 0, 1, "C")

            self.ln(10)

            # Reset colors for body
            self.set_text_color(0, 0, 0)
        except Exception as e:
            print(f"Header error: {e}")

    def footer(self):
        try:
            self.set_y(-15)
            self.set_font('Arial', 'I', 8)
            self.set_text_color(128, 128, 128)
            self.cell(0, 10, f'Generated by {COMPANY_NAME} - Page {self.page_no()}', 0, 0, 'C')
        except Exception as e:
            print(f"Footer error: {e}")

    def add_table(self, data: List[Dict[str, Any]]):
        try:
            if not data:
                self.set_font("Arial", "", 12)
                self.cell(0, 10, "No materials to display", 0, 1, "C")
                return

            # Table header with professional styling
            self.set_fill_color(52, 152, 219)  # Blue header
            self.set_text_color(255, 255, 255)  # White text
            self.set_font("Arial", "B", 11)

            col_width = [45, 80, 25, 25]
            headers = ["Part Number", "Description", "Unit", "Quantity"]

            for i, h in enumerate(headers):
                self.cell(col_width[i], 12, h, 1, 0, "C", True)
            self.ln()

            # Table rows with alternating colors
            self.set_text_color(0, 0, 0)  # Black text
            self.set_font("Arial", "", 10)

            for i, row in enumerate(data):
                # Alternate row colors
                if i % 2 == 0:
                    self.set_fill_color(248, 249, 250)  # Light gray
                else:
                    self.set_fill_color(255, 255, 255)  # White

                # Safely get values with defaults
                part_num = str(row.get("part_number", "N/A"))
                description = str(row.get("description", "N/A"))[:35]  # Truncate
                unit = str(row.get("unit", ""))
                count = str(row.get("count", "0"))

                self.cell(col_width[0], 10, part_num, 1, 0, "L", True)
                self.cell(col_width[1], 10, description, 1, 0, "L", True)
                self.cell(col_width[2], 10, unit, 1, 0, "C", True)
                self.cell(col_width[3], 10, count, 1, 0, "C", True)
                self.ln()

            # Add summary section
            self.ln(5)
            self.set_font("Arial", "B", 10)
            self.set_text_color(0, 0, 0)
            self.cell(0, 8, f"Total Items: {len(data)}", 0, 1)

            # Add note
            self.ln(5)
            self.set_font("Arial", "I", 9)
            self.set_text_color(100, 100, 100)
            self.cell(0, 6, "Note: Quantities include recommended waste factors.", 0, 1)
            self.cell(0, 6, "For technical support, contact Isaiah Industries.", 0, 1)

        except Exception as e:
            print(f"Table generation error: {e}")
            self.set_font("Arial", "", 12)
            self.cell(0, 10, f"Error generating table: {str(e)}", 0, 1)

def darken_color(hex_color, factor=0.8):
    """Darken a hex color by a factor."""
    hex_color = hex_color.lstrip('#')
    rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    darkened = tuple(int(c * factor) for c in rgb)
    return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

def create_card_frame(parent, bg_color=COLORS['card']):
    """Create a modern card-style frame."""
    frame = tk.Frame(parent, bg=bg_color, relief='flat', bd=1)
    frame.config(highlightbackground=COLORS['border'], highlightthickness=1)
    return frame

def run_gui():
    """Main GUI loop with modern Isaiah Industries design."""
    root = tk.Tk()
    root.title(f"{COMPANY_NAME} - Roof Material Analysis Tool")
    root.geometry("800x800")  # Made taller to fit all content
    root.configure(bg=COLORS['background'])
    root.resizable(True, True)  # Allow resizing so user can see everything

    # Set default font
    default_font = ('Segoe UI', 10)
    root.option_add('*Font', default_font)

    # Create scrollable main container
    canvas = tk.Canvas(root, bg=COLORS['background'])
    scrollbar = tk.Scrollbar(root, orient="vertical", command=canvas.yview)
    scrollable_frame = tk.Frame(canvas, bg=COLORS['background'])

    scrollable_frame.bind(
        "<Configure>",
        lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
    )

    canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
    canvas.configure(yscrollcommand=scrollbar.set)

    canvas.pack(side="left", fill="both", expand=True)
    scrollbar.pack(side="right", fill="y")

    # Main container with padding (now inside scrollable frame)
    main_container = tk.Frame(scrollable_frame, bg=COLORS['background'])
    main_container.pack(fill='both', expand=True, padx=30, pady=20)

    # Header section
    header_card = create_card_frame(main_container)
    header_card.pack(fill='x', pady=(0, 20))

    header_content = tk.Frame(header_card, bg=COLORS['card'])
    header_content.pack(fill='x', padx=30, pady=25)

    # Company logo/title
    tk.Label(
        header_content,
        text=COMPANY_NAME,
        font=(default_font[0], 24, 'bold'),
        fg=COLORS['primary'],
        bg=COLORS['card']
    ).pack()

    tk.Label(
        header_content,
        text="Professional Roof Material Analysis Tool",
        font=(default_font[0], 12),
        fg=COLORS['text_secondary'],
        bg=COLORS['card']
    ).pack(pady=(5, 0))

    # Product Selection section
    product_card = create_card_frame(main_container)
    product_card.pack(fill='x', pady=(0, 20))

    product_content = tk.Frame(product_card, bg=COLORS['card'])
    product_content.pack(fill='x', padx=30, pady=25)

    tk.Label(
        product_content,
        text="Step 1: Select Your Product Line",
        font=(default_font[0], 14, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    ).pack(pady=(0, 15))

    # Product selection buttons
    selected_product = tk.StringVar(value="")

    product_buttons_frame = tk.Frame(product_content, bg=COLORS['card'])
    product_buttons_frame.pack(pady=10)

    def select_product(product_name):
        selected_product.set(product_name)
        # Update button colors to show selection
        for btn in product_buttons:
            if btn['text'] == product_name:
                btn.config(bg=COLORS['success'], fg='white', relief='raised')
            else:
                btn.config(bg=COLORS['secondary'], fg='white', relief='flat')

        # Enable and style the main button
        generate_btn.config(
            state='normal',
            bg=COLORS['secondary'],
            fg='white',
            cursor='hand2',
            relief='raised',
            borderwidth=3
        )
        set_status(f"✅ Selected: {product_name} - Now click the big button below!", COLORS['success'])

    # Create product buttons
    product_buttons = []
    for i, product in enumerate(PRODUCTS):
        btn = tk.Button(
            product_buttons_frame,
            text=product,
            command=lambda p=product: select_product(p),
            font=(default_font[0], 12, 'bold'),
            bg=COLORS['secondary'],
            fg='white',
            relief='flat',
            borderwidth=0,
            cursor='hand2',
            padx=20,
            pady=15,
            width=25
        )
        btn.pack(pady=5)
        product_buttons.append(btn)

        # Add hover effects
        def on_enter(event, button=btn):
            if button['bg'] != COLORS['success']:  # Don't change selected button
                button.config(bg=darken_color(COLORS['secondary']))
        def on_leave(event, button=btn):
            if button['bg'] != COLORS['success']:  # Don't change selected button
                button.config(bg=COLORS['secondary'])

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

    # Status section
    status_card = create_card_frame(main_container)
    status_card.pack(fill='x', pady=(0, 20))

    status_content = tk.Frame(status_card, bg=COLORS['card'])
    status_content.pack(fill='x', padx=30, pady=20)

    status_label = tk.Label(
        status_content,
        text="👆 Please select a product line above to get started",
        font=(default_font[0], 11),
        fg=COLORS['warning'],
        bg=COLORS['card']
    )
    status_label.pack()

    def set_status(message: str, color: str = COLORS['text_secondary']):
        status_label.config(text=message, fg=color)
        root.update()

    # Main processing function
    def generate():
        """Main function to process PDF and generate material list."""

        def reset_button():
            generate_btn.config(text="📄 UPLOAD & ANALYZE ROOF REPORT", state='normal')

        # Disable button during processing
        generate_btn.config(text="Processing...", state='disabled')

        try:
            # Step 1: Get the selected product (already chosen via buttons)
            product = selected_product.get()

            if not product:
                set_status("❌ Please select a product line first!", COLORS['accent'])
                reset_button()
                return

            set_status(f"🚀 Processing {product} analysis...", COLORS['secondary'])
            root.update()

            # Step 2: Get PDF file
            set_status("📁 Now select your roof measurement PDF...", COLORS['secondary'])
            root.update()

            pdf_path = filedialog.askopenfilename(
                title="Select Roof Measurement PDF",
                filetypes=[("PDF Files", "*.pdf")]
            )

            if not pdf_path:
                set_status("No file selected.", COLORS['text_secondary'])
                reset_button()
                return

            # Step 3: Extract data from PDF
            set_status("📄 Analyzing PDF content...", COLORS['secondary'])
            data = extract_roof_data(pdf_path, set_status)

            if not data:
                set_status("❌ Failed to extract data from PDF.", COLORS['accent'])
                reset_button()
                return

            # Check if manual input was requested
            if data.get("vendor") == "manual_input":
                set_status("📝 Getting manual measurements...", COLORS['secondary'])

                # Get manual measurements
                measurements = {}
                measurement_prompts = {
                    "total_sqft": "Total Roof Area (square feet)",
                    "ridge_length": "Ridge Length (feet)",
                    "hip_length": "Hip Length (feet)",
                    "valley_length": "Valley Length (feet)",
                    "eaves_length": "Eaves/Perimeter Length (feet)"
                }

                for key, prompt in measurement_prompts.items():
                    while True:
                        value = simpledialog.askstring("Manual Input", f"Enter {prompt}:")
                        if value is None:  # User cancelled
                            set_status("Manual input cancelled.", COLORS['text_secondary'])
                            reset_button()
                            return
                        try:
                            measurements[key] = int(float(value)) if value.strip() else None
                            break
                        except ValueError:
                            messagebox.showerror("Invalid Input", "Please enter a valid number.")

                # Update data with manual measurements
                data.update(measurements)
                data["vendor"] = "manual_input"

            # Step 4: Load parts for the product (we already have the product!)
            set_status("📋 Loading parts database...", COLORS['secondary'])
            parts = load_parts(product)

            if not parts:
                set_status("❌ Failed to load parts database.", COLORS['accent'])
                reset_button()
                return

            # Step 5: Calculate materials
            set_status("🧮 Calculating material quantities...", COLORS['secondary'])
            results = calculate_materials(data, parts)

            if not results:
                set_status("❌ No materials calculated.", COLORS['accent'])
                messagebox.showerror("Calculation Error", "No materials could be calculated from the measurements.")
                reset_button()
                return

            # Step 6: Choose where to save PDF
            set_status("� Choose where to save your PDF...", COLORS['secondary'])
            root.update()

            # Create suggested filename with safe characters only
            import datetime
            import os
            import re
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # Clean product name more thoroughly for filename safety
            safe_product_name = re.sub(r'[^\w\s-]', '', product)  # Remove special chars
            safe_product_name = re.sub(r'\s+', '_', safe_product_name)  # Replace spaces with underscores
            safe_product_name = safe_product_name.strip('_')  # Remove leading/trailing underscores

            if not safe_product_name:  # Fallback if product name becomes empty
                safe_product_name = "Material_List"

            suggested_filename = f"{safe_product_name}_{timestamp}.pdf"

            # Let user choose save location (simplified to avoid dialog issues)
            try:
                # Try with minimal parameters first
                save_path = filedialog.asksaveasfilename(
                    title="Save Material List PDF - Choose Your Folder",
                    defaultextension=".pdf",
                    filetypes=[("PDF Files", "*.pdf")],
                    initialdir=os.path.expanduser("~/Desktop")
                )
            except Exception as dialog_error:
                # Fallback with even simpler dialog
                print(f"Dialog error: {dialog_error}")
                save_path = filedialog.asksaveasfilename(
                    title="Save PDF",
                    filetypes=[("PDF Files", "*.pdf")]
                )

            if not save_path:
                set_status("Save cancelled.", COLORS['text_secondary'])
                reset_button()
                return

            # If user didn't specify a filename, suggest one
            if save_path.lower().endswith('.pdf'):
                # User selected a specific filename
                pass
            else:
                # User just selected a directory, add our suggested filename
                if os.path.isdir(save_path):
                    save_path = os.path.join(save_path, suggested_filename)
                else:
                    # Ensure .pdf extension
                    save_path += '.pdf'

            save_location = os.path.dirname(save_path)
            set_status(f"📄 Creating PDF: {os.path.basename(save_path)}", COLORS['secondary'])

            # Create PDF with enhanced error handling
            try:
                # Create PDF step by step with error checking
                pdf = MaterialListPDF()
                pdf.add_page()

                # Set font safely
                try:
                    pdf.set_font("Arial", "", 10)
                except:
                    pdf.set_font("Arial", "", 12)  # Fallback font size

                # Add header information safely
                try:
                    pdf.cell(0, 10, f"Vendor: {data.get('vendor', 'unknown')}", ln=1)
                    pdf.cell(0, 10, f"Product: {product}", ln=1)
                    pdf.cell(0, 10, f"Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
                    pdf.cell(0, 10, f"Text Length: {data.get('extracted_text_length', 0)} chars", ln=1)
                    pdf.ln(5)
                except Exception as header_error:
                    print(f"Header error: {header_error}")
                    pdf.cell(0, 10, "Material List Report", ln=1)
                    pdf.ln(5)

                # Add the materials table safely
                try:
                    if results and len(results) > 0:
                        pdf.add_table(results)
                    else:
                        pdf.cell(0, 10, "No materials calculated", ln=1)
                except Exception as table_error:
                    print(f"Table error: {table_error}")
                    pdf.cell(0, 10, f"Error creating table: {str(table_error)}", ln=1)

                    # Save the PDF safely
                    try:
                        # Use the simplest output method
                        pdf.output(save_path, 'F')
                        set_status("✅ PDF saved successfully!", COLORS['success'])
                    except Exception as save_error:
                        # Try alternative save method without parameters
                        try:
                            pdf.output(save_path)
                            set_status("✅ PDF saved successfully!", COLORS['success'])
                        except Exception as alt_save_error:
                            # Last resort - try with string output
                            try:
                                pdf_string = pdf.output(dest='S')
                                with open(save_path, 'wb') as f:
                                    if isinstance(pdf_string, str):
                                        f.write(pdf_string.encode('latin1'))
                                    else:
                                        f.write(pdf_string)
                                set_status("✅ PDF saved successfully!", COLORS['success'])
                            except Exception as final_error:
                                raise Exception(f"All save methods failed: {save_error}, {alt_save_error}, {final_error}")

            except Exception as pdf_error:
                error_msg = f"PDF creation failed: {str(pdf_error)}"
                set_status("❌ PDF creation error.", COLORS['accent'])
                messagebox.showerror("PDF Creation Error", error_msg)
                reset_button()
                return

            set_status("✅ Material list generated successfully!", COLORS['success'])

            # Show success with option to open file location
            import os
            import subprocess
            import platform

            def open_file_location():
                try:
                    if platform.system() == "Windows":
                        # Open Windows Explorer and select the file
                        subprocess.run(f'explorer /select,"{save_path}"', shell=True)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", "-R", save_path])
                    else:  # Linux
                        subprocess.run(["xdg-open", os.path.dirname(save_path)])
                except Exception as e:
                    messagebox.showerror("Error", f"Could not open file location: {e}")

            # Create custom dialog with file location options
            result = messagebox.askyesno(
                "✅ PDF Generated Successfully - Isaiah Industries",
                f"Professional material list saved to your chosen location!\n\n"
                f"� Filename: {os.path.basename(save_path)}\n"
                f"� Saved to: {os.path.basename(save_location)}\n"
                f"🗂️ Full path: {save_path}\n\n"
                f"Would you like to open the folder to view your PDF?"
            )

            if result:  # User clicked Yes
                open_file_location()

        except Exception as e:
            set_status("❌ Failed to generate PDF report.", COLORS['accent'])
            messagebox.showerror("PDF Generation Error", f"Failed to generate PDF: {e}")
        finally:
            reset_button()

    # Action section - Large prominent button
    action_card = create_card_frame(main_container)
    action_card.pack(fill='x', pady=(0, 20))

    action_content = tk.Frame(action_card, bg=COLORS['card'])
    action_content.pack(fill='x', padx=40, pady=40)

    # Step 2 label
    tk.Label(
        action_content,
        text="Step 2: Upload Your PDF",
        font=(default_font[0], 14, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    ).pack(pady=(0, 15))

    # Large, prominent main action button (simplified - no shadow)
    button_frame = tk.Frame(action_content, bg=COLORS['card'])
    button_frame.pack(pady=30)

    # Simple, visible main button
    generate_btn = tk.Button(
        button_frame,
        text="📄 UPLOAD & ANALYZE ROOF REPORT",
        command=generate,
        font=(default_font[0], 18, 'bold'),
        bg='#DDDDDD',  # Light gray but clearly visible
        fg='#333333',  # Dark text for good contrast
        relief='raised',
        borderwidth=3,
        cursor='arrow',
        padx=30,
        pady=20,
        width=35,
        height=3,
        state='disabled'  # Disabled until product is selected
    )
    generate_btn.pack(pady=10)

    # Simple hover effects for the button (only when enabled)
    def on_enter_large(_):
        if generate_btn['state'] == 'normal':
            generate_btn.config(
                bg=darken_color(COLORS['secondary']),
                relief='raised',
                borderwidth=4
            )
    def on_leave_large(_):
        if generate_btn['state'] == 'normal':
            generate_btn.config(
                bg=COLORS['secondary'],
                relief='raised',
                borderwidth=3
            )

    generate_btn.bind("<Enter>", on_enter_large)
    generate_btn.bind("<Leave>", on_leave_large)

    # Help text
    tk.Label(
        action_content,
        text="💡 Step 2: After selecting a product above, click here to upload your PDF",
        font=(default_font[0], 11, 'italic'),
        fg=COLORS['warning'],
        bg=COLORS['card']
    ).pack(pady=(15, 0))

    # Supported formats
    help_frame = tk.Frame(action_content, bg=COLORS['card'])
    help_frame.pack(pady=(20, 0))

    tk.Label(
        help_frame,
        text="✅ Supported PDF Formats:",
        font=(default_font[0], 12, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    ).pack()

    tk.Label(
        help_frame,
        text="EagleView • RoofSnap • Hover • Roofr • GAF • Xactimate • Any PDF with roof measurements",
        font=(default_font[0], 11),
        fg=COLORS['text_secondary'],
        bg=COLORS['card'],
        wraplength=600
    ).pack(pady=(8, 0))

    # Footer section
    footer_frame = tk.Frame(main_container, bg=COLORS['background'])
    footer_frame.pack(side="bottom", fill='x', pady=(20, 0))

    footer_content = tk.Frame(footer_frame, bg=COLORS['background'])
    footer_content.pack()

    # Company info
    tk.Label(
        footer_content,
        text=f"© 2024 {COMPANY_NAME} - Professional Roofing Solutions",
        font=('Segoe UI', 9),
        fg=COLORS['text_secondary'],
        bg=COLORS['background']
    ).pack()

    # Version info
    tk.Label(
        footer_content,
        text="Version 2.1 - Fixed & Enhanced",
        font=('Segoe UI', 8),
        fg=COLORS['text_secondary'],
        bg=COLORS['background']
    ).pack()

    root.mainloop()

if __name__ == "__main__":
    run_gui()
