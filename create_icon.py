"""
Create a simple icon for the Isaiah Industries Roof Tool
"""
from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    """Create a professional icon for the application."""
    # Create a 256x256 image with transparent background
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    primary_color = (44, 62, 80)      # Dark blue
    secondary_color = (52, 152, 219)  # Bright blue
    accent_color = (231, 76, 60)      # Red
    
    # Draw background circle
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=primary_color, outline=secondary_color, width=8)
    
    # Draw roof shape
    roof_points = [
        (size//2, margin + 40),      # Top point
        (margin + 40, size//2 + 20), # Left point
        (size - margin - 40, size//2 + 20)  # Right point
    ]
    draw.polygon(roof_points, fill=secondary_color)
    
    # Draw house base
    house_left = margin + 60
    house_right = size - margin - 60
    house_top = size//2 + 20
    house_bottom = size - margin - 40
    
    draw.rectangle([house_left, house_top, house_right, house_bottom], 
                  fill=accent_color)
    
    # Add "I" for <PERSON>
    try:
        # Try to use a system font
        font_size = 80
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    # Draw "I" in the center
    text = "I"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    text_x = (size - text_width) // 2
    text_y = (size - text_height) // 2 + 10
    
    draw.text((text_x, text_y), text, fill='white', font=font)
    
    # Save as ICO file
    img.save('icon.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
    print("✅ Icon created: icon.ico")
    
    # Also save as PNG for preview
    img.save('icon_preview.png', format='PNG')
    print("✅ Preview created: icon_preview.png")

if __name__ == "__main__":
    try:
        create_icon()
    except Exception as e:
        print(f"❌ Error creating icon: {e}")
        print("Icon creation failed, but the app will still work without it.")
