name: Build Isaiah Industries Roof Tool

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  release:
    types: [ published ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [3.8, 3.9, '3.10', '3.11']

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest
    
    - name: Run tests
      run: |
        python test_pdf_reading.py
        python quick_test.py

  build-windows:
    needs: test
    runs-on: windows-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Create icon
      run: python create_icon.py
    
    - name: Build Windows executable
      run: |
        if (Test-Path icon.ico) {
          pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --icon=icon.ico --add-data "parts.csv;." --hidden-import=pdfplumber roof_material_tool.py
        } else {
          pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv;." --hidden-import=pdfplumber roof_material_tool.py
        }
    
    - name: Upload Windows artifact
      uses: actions/upload-artifact@v3
      with:
        name: windows-executable
        path: dist/Isaiah_Industries_Roof_Tool.exe

  build-mac:
    needs: test
    runs-on: macos-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Create icon
      run: python create_icon.py
    
    - name: Build Mac application
      run: |
        if [ -f "icon.ico" ]; then
          sips -s format icns icon.ico --out icon.icns 2>/dev/null || true
          pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --icon=icon.icns --add-data "parts.csv:." roof_material_tool_mac.py
        else
          pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv:." roof_material_tool_mac.py
        fi
    
    - name: Upload Mac artifact
      uses: actions/upload-artifact@v3
      with:
        name: mac-executable
        path: dist/Isaiah_Industries_Roof_Tool

  release:
    needs: [build-windows, build-mac]
    runs-on: ubuntu-latest
    if: github.event_name == 'release'
    
    steps:
    - name: Download Windows artifact
      uses: actions/download-artifact@v3
      with:
        name: windows-executable
        path: ./windows/
    
    - name: Download Mac artifact
      uses: actions/download-artifact@v3
      with:
        name: mac-executable
        path: ./mac/
    
    - name: Upload Release Assets
      uses: softprops/action-gh-release@v1
      with:
        files: |
          ./windows/Isaiah_Industries_Roof_Tool.exe
          ./mac/Isaiah_Industries_Roof_Tool
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
