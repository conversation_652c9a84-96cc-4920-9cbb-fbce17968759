"""
Test the manual save feature
"""
import tkinter as tk
from tkinter import filedialog, messagebox
import os
import datetime

def test_manual_save_dialog():
    """Test the manual save dialog functionality."""
    print("🧪 Testing Manual Save Feature")
    print("=" * 35)
    
    # Create a simple test window
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    # Create suggested filename (same logic as main app)
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    product = "Country Manor Shake"
    safe_product_name = "".join(c for c in product if c.isalnum() or c in (' ', '-', '_')).rstrip()
    safe_product_name = safe_product_name.replace(' ', '_')
    suggested_filename = f"{safe_product_name}_Material_List_{timestamp}.pdf"
    
    print(f"📋 Product: {product}")
    print(f"📄 Suggested filename: {suggested_filename}")
    print(f"📁 Starting directory: Desktop")
    
    print(f"\n🎯 Testing file save dialog...")
    print(f"This will open a 'Save As' dialog where you can:")
    print(f"  • Choose any folder you want")
    print(f"  • Create new folders")
    print(f"  • Rename the file")
    print(f"  • Organize your PDFs however you like")
    
    # Test the save dialog (same as main app)
    save_path = filedialog.asksaveasfilename(
        title="Save Material List PDF - Choose Your Folder",
        defaultextension=".pdf",
        filetypes=[
            ("PDF Files", "*.pdf"),
            ("All Files", "*.*")
        ],
        initialname=suggested_filename,
        initialdir=os.path.expanduser("~/Desktop")  # Start at Desktop
    )
    
    root.destroy()
    
    if save_path:
        # Ensure .pdf extension
        if not save_path.lower().endswith('.pdf'):
            save_path += '.pdf'
        
        save_location = os.path.dirname(save_path)
        filename = os.path.basename(save_path)
        
        print(f"\n✅ Save location chosen!")
        print(f"📄 Filename: {filename}")
        print(f"📁 Folder: {save_location}")
        print(f"🗂️ Full path: {save_path}")
        
        # Test if we can write to that location
        try:
            # Create a test file to verify we can write there
            test_content = f"Test PDF content for {product}\nGenerated: {datetime.datetime.now()}"
            with open(save_path.replace('.pdf', '_test.txt'), 'w') as f:
                f.write(test_content)
            
            print(f"✅ Can write to chosen location")
            
            # Clean up test file
            os.unlink(save_path.replace('.pdf', '_test.txt'))
            
            # Show success dialog (same as main app)
            result = messagebox.askyesno(
                "✅ Test Successful - Manual Save Works!",
                f"Manual save feature is working!\n\n"
                f"📄 Would save as: {filename}\n"
                f"📁 In folder: {os.path.basename(save_location)}\n"
                f"🗂️ Full path: {save_path}\n\n"
                f"Would you like to open the folder?"
            )
            
            if result:
                # Open the folder
                import subprocess
                import platform
                
                try:
                    if platform.system() == "Windows":
                        subprocess.run(f'explorer "{save_location}"', shell=True)
                    elif platform.system() == "Darwin":  # macOS
                        subprocess.run(["open", save_location])
                    else:  # Linux
                        subprocess.run(["xdg-open", save_location])
                    print(f"✅ Opened folder: {save_location}")
                except Exception as e:
                    print(f"❌ Could not open folder: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Cannot write to chosen location: {e}")
            messagebox.showerror("Error", f"Cannot write to chosen location: {e}")
            return False
    else:
        print(f"\n❌ Save cancelled by user")
        return False

def main():
    """Test the manual save feature."""
    print("🏠 Isaiah Industries - Manual Save Test")
    print("🗂️ Testing the ability to choose save location and organize PDFs")
    
    print(f"\n💡 Manual Save Benefits:")
    print("✅ Choose exactly where to save PDFs")
    print("✅ Create folders for different projects")
    print("✅ Organize by customer, date, or product")
    print("✅ Rename files as needed")
    print("✅ Save to network drives or cloud folders")
    print("✅ Professional file organization")
    
    print(f"\n🎯 How it works:")
    print("1. Select product and upload PDF")
    print("2. Tool processes the measurements")
    print("3. 'Save As' dialog opens")
    print("4. Choose your folder and filename")
    print("5. PDF saves to your chosen location")
    print("6. Option to open the folder automatically")
    
    input("\nPress Enter to test the save dialog...")
    
    test_result = test_manual_save_dialog()
    
    print(f"\n🎯 TEST RESULT: {'PASS' if test_result else 'FAIL'}")
    
    if test_result:
        print(f"\n🎉 Manual save feature is working perfectly!")
        print(f"\n💡 Suggested folder organization:")
        print("📁 Documents/")
        print("  📁 Isaiah Industries/")
        print("    📁 Material Lists/")
        print("      📁 2024/")
        print("        📁 December/")
        print("          📄 Country_Manor_Shake_Material_List_20241220_143052.pdf")
        print("          📄 Oxford_Shingle_Material_List_20241220_144123.pdf")
    else:
        print(f"\n⚠️ Test failed or was cancelled")
    
    print(f"\n🚀 The tool now provides:")
    print("• Complete control over where PDFs are saved")
    print("• Suggested filenames with timestamps")
    print("• Ability to create and organize folders")
    print("• Professional file management")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
