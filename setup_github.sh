#!/bin/bash

echo "🏠 Isaiah Industries Roof Tool - GitHub Setup"
echo "=============================================="
echo

# Check if git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

echo "📋 This script will help you set up your GitHub repository"
echo

# Get repository information
read -p "Enter your GitHub username: " GITHUB_USERNAME
read -p "Enter repository name (default: isaiah-roof-tool): " REPO_NAME
REPO_NAME=${REPO_NAME:-isaiah-roof-tool}

echo
echo "🔧 Setting up local repository..."

# Initialize git if not already done
if [ ! -d ".git" ]; then
    git init
    echo "✅ Git repository initialized"
else
    echo "✅ Git repository already exists"
fi

# Add all files
git add .
echo "✅ Files staged for commit"

# Create initial commit
if ! git log --oneline -1 &> /dev/null; then
    git commit -m "Initial commit: Isaiah Industries Roof Material Analysis Tool

- Modern professional UI with Isaiah Industries branding
- Enhanced PDF text extraction (no OCR dependencies)
- Cross-platform support (Windows and Mac)
- Commercial-grade material calculations
- Professional PDF output
- Comprehensive documentation and build scripts
- GitHub workflows for automated building
- Issue templates for continuous development"
    echo "✅ Initial commit created"
else
    echo "✅ Repository already has commits"
fi

# Set up remote
REMOTE_URL="https://github.com/${GITHUB_USERNAME}/${REPO_NAME}.git"
if ! git remote get-url origin &> /dev/null; then
    git remote add origin $REMOTE_URL
    echo "✅ Remote origin added: $REMOTE_URL"
else
    echo "✅ Remote origin already exists"
fi

# Set main branch
git branch -M main
echo "✅ Main branch set"

echo
echo "🚀 Repository setup complete!"
echo
echo "📋 Next steps:"
echo "1. Create repository on GitHub:"
echo "   - Go to https://github.com/new"
echo "   - Repository name: $REPO_NAME"
echo "   - Description: Professional roof material analysis tool for contractors"
echo "   - Make it public or private as needed"
echo "   - Don't initialize with README (we already have one)"
echo
echo "2. Push to GitHub:"
echo "   git push -u origin main"
echo
echo "3. Set up GitHub features:"
echo "   - Enable Issues"
echo "   - Enable Discussions"
echo "   - Enable GitHub Actions"
echo "   - Add topics: roofing, contractors, pdf-processing, material-analysis"
echo
echo "4. Create initial issues from INITIAL_ISSUES.md"
echo
echo "5. Enable GitHub Copilot or other AI agents"
echo
echo "🎯 Your repository will be available at:"
echo "   https://github.com/${GITHUB_USERNAME}/${REPO_NAME}"
echo
echo "🤖 For AI agent development:"
echo "   - Issues are ready in INITIAL_ISSUES.md"
echo "   - GitHub Actions will build automatically"
echo "   - Contributing guidelines are in .github/CONTRIBUTING.md"
echo
echo "Happy coding! 🏠"
