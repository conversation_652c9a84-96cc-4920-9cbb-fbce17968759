"""
Test the parts loading functionality
"""
import os
import pandas as pd

def test_parts_csv():
    """Test if parts.csv exists and is readable."""
    print("🔍 Testing Parts Database Loading...")
    print("=" * 50)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # List files in current directory
    print(f"\n📋 Files in current directory:")
    for file in os.listdir(current_dir):
        if file.endswith('.csv') or file.endswith('.py'):
            print(f"  • {file}")
    
    # Test possible CSV locations
    csv_paths = [
        "parts.csv",
        os.path.join(current_dir, "parts.csv"),
        os.path.join(os.path.dirname(__file__), "parts.csv"),
    ]
    
    print(f"\n🔍 Testing CSV file locations:")
    csv_found = None
    
    for path in csv_paths:
        exists = os.path.exists(path)
        print(f"  {'✅' if exists else '❌'} {path}")
        if exists and csv_found is None:
            csv_found = path
    
    if not csv_found:
        print(f"\n❌ parts.csv not found in any location!")
        print(f"Creating a sample parts.csv file...")
        create_sample_csv()
        csv_found = "parts.csv"
    
    # Test loading the CSV
    print(f"\n📊 Testing CSV loading from: {csv_found}")
    try:
        df = pd.read_csv(csv_found)
        print(f"✅ CSV loaded successfully!")
        print(f"  • Rows: {len(df)}")
        print(f"  • Columns: {list(df.columns)}")
        
        if "product" in df.columns:
            products = df["product"].unique()
            print(f"  • Products: {list(products)}")
            
            # Test filtering
            for product in products:
                product_parts = df[df["product"].str.lower() == product.lower()]
                print(f"    - {product}: {len(product_parts)} parts")
        else:
            print(f"❌ Missing 'product' column!")
            
    except Exception as e:
        print(f"❌ Error loading CSV: {e}")

def create_sample_csv():
    """Create a sample parts.csv file."""
    print("📝 Creating sample parts.csv...")
    
    sample_data = [
        {"product": "Country Manor Shake", "part_number": "CMS-001", "description": "Country Manor Shake Panels", "source_metric": "total_sqft", "coverage": 100, "waste": 0.10, "unit": "sq ft"},
        {"product": "Country Manor Shake", "part_number": "CMS-002", "description": "Ridge Cap", "source_metric": "ridge_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        {"product": "Country Manor Shake", "part_number": "CMS-003", "description": "Hip Cap", "source_metric": "hip_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        {"product": "Country Manor Shake", "part_number": "CMS-004", "description": "Valley Flashing", "source_metric": "valley_length", "coverage": 10, "waste": 0.10, "unit": "linear ft"},
        {"product": "Country Manor Shake", "part_number": "CMS-005", "description": "Eave Trim", "source_metric": "eaves_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        {"product": "Oxford Shingle", "part_number": "OXS-001", "description": "Oxford Shingle Panels", "source_metric": "total_sqft", "coverage": 100, "waste": 0.10, "unit": "sq ft"},
        {"product": "Oxford Shingle", "part_number": "OXS-002", "description": "Ridge Cap", "source_metric": "ridge_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        {"product": "Oxford Shingle", "part_number": "OXS-003", "description": "Hip Cap", "source_metric": "hip_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        {"product": "Oxford Shingle", "part_number": "OXS-004", "description": "Valley Flashing", "source_metric": "valley_length", "coverage": 10, "waste": 0.10, "unit": "linear ft"},
        {"product": "Oxford Shingle", "part_number": "OXS-005", "description": "Eave Trim", "source_metric": "eaves_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
    ]
    
    df = pd.DataFrame(sample_data)
    df.to_csv("parts.csv", index=False)
    print("✅ Sample parts.csv created!")

def test_load_parts_function():
    """Test the actual load_parts function."""
    print(f"\n🧪 Testing load_parts function...")
    
    try:
        # Import the function
        import sys
        sys.path.append('.')
        from roof_material_tool import load_parts
        
        # Test with valid product
        print(f"Testing with 'Country Manor Shake'...")
        parts = load_parts("Country Manor Shake")
        
        if parts:
            print(f"✅ Found {len(parts)} parts for Country Manor Shake")
            for part in parts[:3]:  # Show first 3
                print(f"  • {part['part_number']}: {part['description']}")
        else:
            print(f"❌ No parts found for Country Manor Shake")
        
        # Test with invalid product
        print(f"\nTesting with invalid product...")
        parts = load_parts("Invalid Product")
        print(f"Result: {len(parts)} parts (should be 0)")
        
    except ImportError as e:
        print(f"❌ Could not import load_parts function: {e}")
    except Exception as e:
        print(f"❌ Error testing load_parts: {e}")

def main():
    """Run all tests."""
    print("🏠 Isaiah Industries - Parts Loading Test")
    print("🔧 Testing parts database functionality")
    
    test_parts_csv()
    test_load_parts_function()
    
    print(f"\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print("✅ Parts CSV location tested")
    print("✅ CSV loading functionality tested")
    print("✅ Product filtering tested")
    print("✅ Error handling tested")
    
    print(f"\n💡 If you're still getting 'parts not found' errors:")
    print("1. Make sure parts.csv is in the same folder as the .exe")
    print("2. Or in the same folder as roof_material_tool.py")
    print("3. Check that the CSV has the correct format")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
