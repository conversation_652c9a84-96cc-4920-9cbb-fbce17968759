"""
Test the fixed version of the roof material tool
"""
import sys
import os

def test_imports():
    """Test that all imports work correctly."""
    print("🧪 Testing Fixed Version Imports")
    print("=" * 50)
    
    try:
        sys.path.append('.')
        
        # Test basic imports
        print("📦 Testing basic imports...")
        import pdfplumber
        print("  ✅ pdfplumber imported")
        
        import tkinter as tk
        print("  ✅ tkinter imported")
        
        from fpdf import FPDF
        print("  ✅ fpdf imported")
        
        import pandas as pd
        print("  ✅ pandas imported")
        
        # Test main module import
        print("\n📦 Testing main module...")
        from roof_material_tool import extract_roof_data, analyze_text_for_measurements, calculate_materials, MaterialListPDF
        print("  ✅ Main functions imported successfully")
        
        print("\n✅ ALL IMPORTS SUCCESSFUL!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_text_analysis():
    """Test the text analysis functionality."""
    print(f"\n🔍 Testing Text Analysis")
    print("=" * 50)
    
    try:
        from roof_material_tool import analyze_text_for_measurements
        
        # Test with sample roof measurement text
        sample_texts = [
            "Total Roof Area: 2,450 sq ft\nRidge Length: 125 ft\nHip: 45 feet\nValley Length = 78 ft\nEave Length: 180 ft",
            "Building Area 1850 square feet Ridge 95 linear feet Valley 65 ft Perimeter 220 feet",
            "Area: 2100 SF Ridge: 110 ft Hip: 38 ft Valley: 55 ft Eave: 195 ft",
            "2450 1250 125 45 78 180",  # Just numbers
        ]
        
        for i, text in enumerate(sample_texts):
            print(f"\n📝 Test {i+1}: {text[:50]}...")
            
            result = analyze_text_for_measurements(text)
            
            measurements_found = sum(1 for v in result.values() if isinstance(v, int) and v > 0)
            print(f"   Found {measurements_found} measurements:")
            
            for key, value in result.items():
                if isinstance(value, int) and value > 0:
                    print(f"     • {key}: {value}")
            
            if measurements_found > 0:
                print(f"   ✅ SUCCESS!")
            else:
                print(f"   ⚠️ No measurements found")
        
        print(f"\n✅ Text analysis test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Text analysis test failed: {e}")
        return False

def test_pdf_generation():
    """Test PDF generation functionality."""
    print(f"\n📄 Testing PDF Generation")
    print("=" * 50)
    
    try:
        from roof_material_tool import MaterialListPDF, calculate_materials
        import tempfile
        import pandas as pd
        
        # Create sample data
        sample_data = {
            "vendor": "test",
            "extracted_text_length": 100,
            "total_sqft": 2000,
            "ridge_length": 120,
            "hip_length": 45,
            "valley_length": 50,
            "eaves_length": 180
        }
        
        sample_parts = [
            {"part_number": "TEST-001", "description": "Test Panel", "source_metric": "total_sqft", "coverage": 100, "waste": 0.10, "unit": "sq ft"},
            {"part_number": "TEST-002", "description": "Test Ridge", "source_metric": "ridge_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        ]
        
        print(f"🧮 Testing material calculations...")
        results = calculate_materials(sample_data, sample_parts)
        
        if results:
            print(f"✅ Calculated {len(results)} material items")
            for result in results:
                print(f"  • {result['part_number']}: {result['description']} - Qty: {result['count']}")
        else:
            print(f"❌ No materials calculated")
            return False
        
        print(f"\n📄 Testing PDF creation...")
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            pdf = MaterialListPDF()
            pdf.add_page()
            pdf.set_font("Arial", "", 10)
            
            # Add header information
            pdf.cell(0, 10, f"Vendor Detected: {sample_data.get('vendor', 'unknown')}", ln=1)
            pdf.cell(0, 10, f"Product Line: Test Product", ln=1)
            pdf.cell(0, 10, f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
            pdf.cell(0, 10, f"Text Extracted: {sample_data.get('extracted_text_length', 0)} characters", ln=1)
            pdf.ln(5)
            
            # Add the materials table
            pdf.add_table(results)
            
            # Save the PDF
            pdf.output(temp_path)
            
            print(f"✅ PDF created successfully!")
            
            # Check if file exists and has content
            if os.path.exists(temp_path):
                file_size = os.path.getsize(temp_path)
                print(f"  • File size: {file_size} bytes")
                
                if file_size > 1000:  # Should be at least 1KB for a valid PDF
                    print(f"✅ PDF appears to be valid")
                    return True
                else:
                    print(f"⚠️ PDF file is very small, might be corrupted")
                    return False
            else:
                print(f"❌ PDF file was not created")
                return False
                
        except Exception as e:
            print(f"❌ PDF creation failed: {e}")
            return False
        finally:
            # Clean up
            try:
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
            except:
                pass
                
    except Exception as e:
        print(f"❌ PDF generation test failed: {e}")
        return False

def test_gui_startup():
    """Test that the GUI can start without errors."""
    print(f"\n🖥️ Testing GUI Startup")
    print("=" * 50)
    
    try:
        import tkinter as tk
        from roof_material_tool import COLORS, create_card_frame, darken_color
        
        print("🎨 Testing UI components...")
        
        # Test color definitions
        print(f"  ✅ Colors defined: {len(COLORS)} colors")
        
        # Test utility functions
        darker = darken_color(COLORS['secondary'])
        print(f"  ✅ Color darkening works: {COLORS['secondary']} -> {darker}")
        
        # Test basic GUI creation (without mainloop)
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        frame = create_card_frame(root)
        print(f"  ✅ Card frame creation works")
        
        root.destroy()
        
        print(f"✅ GUI components test passed!")
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        return False

def main():
    """Run all tests for the fixed version."""
    print("🏠 Isaiah Industries - Fixed Version Test")
    print("🔧 Testing the simplified, working version")
    
    print(f"\n🎯 This version should fix:")
    print("• PDF generation errors")
    print("• Complex extraction issues")
    print("• OCR dependencies")
    print("• Import problems")
    
    # Run tests
    import_test = test_imports()
    text_test = test_text_analysis()
    pdf_test = test_pdf_generation()
    gui_test = test_gui_startup()
    
    print(f"\n" + "=" * 60)
    print("🎯 TEST RESULTS:")
    print(f"✅ Imports: {'PASSED' if import_test else 'FAILED'}")
    print(f"✅ Text Analysis: {'PASSED' if text_test else 'FAILED'}")
    print(f"✅ PDF Generation: {'PASSED' if pdf_test else 'FAILED'}")
    print(f"✅ GUI Components: {'PASSED' if gui_test else 'FAILED'}")
    
    all_passed = import_test and text_test and pdf_test and gui_test
    
    if all_passed:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The fixed version should work correctly.")
        print(f"\n💡 Next steps:")
        print("1. Run: python roof_material_tool.py")
        print("2. Test with your PDF files")
        print("3. If it works, rebuild: build.bat")
    else:
        print(f"\n⚠️ Some tests failed. Check the errors above.")
    
    print(f"\n🔧 Key improvements in this version:")
    print("• Removed OCR dependencies (no more pytesseract/pdf2image)")
    print("• Simplified PDF extraction (3 methods instead of 8)")
    print("• Fixed PDF generation errors")
    print("• Better error handling")
    print("• Cleaner, more reliable code")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
