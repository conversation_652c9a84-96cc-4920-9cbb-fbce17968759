@echo off
echo ========================================
echo Isaiah Industries Roof Tool - EXE Builder
echo ========================================
echo.

echo Installing required packages...
pip install -r requirements.txt

echo.
echo Building executable...
pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv;." roof_material_tool.py

echo.
echo ========================================
echo Build Complete!
echo ========================================
echo Your executable is in the 'dist' folder:
echo dist\Isaiah_Industries_Roof_Tool.exe
echo.
echo To distribute to coworkers:
echo 1. Copy the .exe file
echo 2. Install Tesseract OCR on target computers
echo 3. Run the .exe file
echo.
pause
