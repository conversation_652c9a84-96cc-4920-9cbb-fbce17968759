@echo off
echo ========================================
echo Isaiah Industries Roof Tool - EXE Builder
echo Modern Professional Version 2.0
echo ========================================
echo.

echo Installing required packages...
pip install -r requirements.txt

echo.
echo Creating application icon...
python create_icon.py

echo.
echo Building modern executable...
if exist icon.ico (
    pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --icon=icon.ico --add-data "parts.csv;." roof_material_tool.py
) else (
    pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv;." roof_material_tool.py
)

echo.
echo ========================================
echo Build Complete!
echo ========================================
echo Your modern executable is in the 'dist' folder:
echo dist\Isaiah_Industries_Roof_Tool.exe
echo.
echo NEW FEATURES:
echo - Modern professional UI design
echo - Improved user experience
echo - Commercial-grade appearance
echo - Enhanced error handling
echo - Professional PDF output
echo.
echo To distribute to contractors:
echo 1. Copy the .exe file
echo 2. Install Tesseract OCR on target computers
echo 3. Run the .exe file
echo 4. Ready for commercial use!
echo.
pause
