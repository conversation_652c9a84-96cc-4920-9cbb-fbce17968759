# Contributing to Isaiah Industries Roof Tool

Thank you for your interest in contributing to the Isaiah Industries Roof Material Analysis Tool! This project welcomes both human and AI agent contributions.

## 🤖 **For AI Agents**

### **Getting Started**
1. Review open [Issues](https://github.com/yourusername/isaiah-roof-tool/issues)
2. Look for issues labeled `good-first-issue` or `ai-ready`
3. Comment on the issue to claim it
4. Fork the repository and create a feature branch

### **Development Guidelines**
- **Code Style**: Follow PEP 8 for Python code
- **Testing**: All new features must include tests
- **Documentation**: Update relevant documentation
- **Commits**: Use conventional commit messages

### **AI Agent Workflow**
1. **Analyze** the issue requirements thoroughly
2. **Plan** the implementation approach
3. **Implement** with proper error handling
4. **Test** thoroughly on both Windows and Mac
5. **Document** changes and new features
6. **Submit** PR with detailed description

## 👥 **For Human Contributors**

### **Types of Contributions**
- Bug fixes
- Feature enhancements
- Documentation improvements
- Test coverage
- Performance optimizations
- UI/UX improvements

### **Development Setup**
```bash
git clone https://github.com/yourusername/isaiah-roof-tool.git
cd isaiah-roof-tool
pip install -r requirements.txt
```

### **Testing**
```bash
python test_pdf_reading.py
python debug_pdf.py
python quick_test.py
```

### **Building**
**Windows:**
```bash
build.bat
```

**Mac:**
```bash
chmod +x build_mac.sh
./build_mac.sh
```

## 📋 **Coding Standards**

### **Python Code Style**
- Follow PEP 8
- Use type hints
- Maximum line length: 100 characters
- Use descriptive variable names
- Add docstrings to all functions

### **File Organization**
- Keep main application files in root
- Put utilities in `utils/` folder
- Tests in `tests/` folder
- Documentation in `docs/` folder

### **Error Handling**
- Use specific exception types
- Provide helpful error messages
- Log errors appropriately
- Graceful degradation when possible

## 🧪 **Testing Requirements**

### **Required Tests**
- Unit tests for all new functions
- Integration tests for PDF processing
- Cross-platform compatibility tests
- Performance tests for large files

### **Test Coverage**
- Minimum 80% code coverage
- All critical paths must be tested
- Edge cases should be covered

## 📝 **Documentation**

### **Code Documentation**
- Docstrings for all public functions
- Inline comments for complex logic
- Type hints for all parameters

### **User Documentation**
- Update README.md for new features
- Add examples for new functionality
- Update installation instructions if needed

## 🔄 **Pull Request Process**

### **Before Submitting**
1. Ensure all tests pass
2. Update documentation
3. Add changelog entry
4. Verify cross-platform compatibility

### **PR Description Template**
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No breaking changes
```

## 🏷️ **Issue Labels**

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Improvements or additions to docs
- `good-first-issue` - Good for newcomers
- `ai-ready` - Ready for AI agent development
- `help-wanted` - Extra attention is needed
- `priority-high` - High priority issue
- `platform-windows` - Windows-specific
- `platform-mac` - Mac-specific

## 🎯 **Development Priorities**

### **High Priority**
1. PDF processing improvements
2. Cross-platform compatibility
3. Error handling enhancements
4. Performance optimizations

### **Medium Priority**
1. UI/UX improvements
2. Additional vendor support
3. Export format options
4. Batch processing

### **Low Priority**
1. Advanced features
2. Integration capabilities
3. Mobile versions
4. Web interface

## 📞 **Getting Help**

- **Questions**: Use [Discussions](https://github.com/yourusername/isaiah-roof-tool/discussions)
- **Bugs**: Create an [Issue](https://github.com/yourusername/isaiah-roof-tool/issues)
- **Features**: Submit a [Feature Request](https://github.com/yourusername/isaiah-roof-tool/issues/new?template=feature_request.md)

## 📄 **License**

By contributing, you agree that your contributions will be licensed under the same license as the project.

---

**Thank you for contributing to the Isaiah Industries Roof Tool!** 🏠
