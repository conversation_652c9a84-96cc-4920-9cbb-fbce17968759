"""
Debug script to test PDF reading and parts loading
"""
import pdfplumber
import pandas as pd
import re
from tkinter import filedialog, messagebox
import tkinter as tk

def debug_pdf_reading():
    """Test PDF reading with detailed output."""
    print("🔍 PDF Reading Debug Tool")
    print("=" * 50)
    
    # Create a simple file dialog
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    pdf_path = filedialog.askopenfilename(
        title="Select a PDF to debug",
        filetypes=[("PDF Files", "*.pdf")]
    )
    
    if not pdf_path:
        print("No PDF selected.")
        return
    
    print(f"📄 Testing PDF: {pdf_path}")
    print("-" * 50)
    
    try:
        with pdfplumber.open(pdf_path) as pdf:
            print(f"✅ PDF opened successfully")
            print(f"📊 Number of pages: {len(pdf.pages)}")
            
            all_text = ""
            
            for i, page in enumerate(pdf.pages):
                print(f"\n📄 Page {i+1}:")
                
                # Method 1: Standard extraction
                text = page.extract_text()
                if text:
                    print(f"  ✅ Standard extraction: {len(text)} characters")
                    all_text += text + "\n"
                    # Show first 200 characters
                    preview = text[:200].replace('\n', ' ')
                    print(f"  Preview: {preview}...")
                else:
                    print(f"  ❌ Standard extraction: No text found")
                
                # Method 2: Layout extraction
                try:
                    layout_text = page.extract_text(layout=True)
                    if layout_text and layout_text != text:
                        print(f"  ✅ Layout extraction: {len(layout_text)} characters")
                        all_text += layout_text + "\n"
                except:
                    print(f"  ❌ Layout extraction failed")
                
                # Method 3: Tables
                try:
                    tables = page.extract_tables()
                    if tables:
                        print(f"  ✅ Found {len(tables)} tables")
                        for j, table in enumerate(tables):
                            print(f"    Table {j+1}: {len(table)} rows")
                            # Add table content to text
                            for row in table:
                                if row:
                                    row_text = " ".join([str(cell) if cell else "" for cell in row])
                                    all_text += row_text + "\n"
                    else:
                        print(f"  ❌ No tables found")
                except:
                    print(f"  ❌ Table extraction failed")
            
            print(f"\n📊 Total extracted text: {len(all_text)} characters")
            
            if all_text.strip():
                print("\n🔍 Looking for measurements...")
                
                # Test measurement patterns
                patterns = {
                    "total_sqft": [
                        r"Total\s+Area[:=]?\s*([\d,]+\.?\d*)",
                        r"Roof\s+Area[:=]?\s*([\d,]+\.?\d*)",
                        r"(\d{1,6}\.?\d*)\s*sq\.?\s*ft\.?\s*total",
                    ],
                    "ridge_length": [
                        r"Ridge\s+Length[:=]?\s*([\d,]+\.?\d*)",
                        r"Ridge[:=]?\s*([\d,]+\.?\d*)\s*(?:ft|feet)",
                    ],
                    "numbers": [r"(\d{1,6}\.?\d*)"]  # Any numbers
                }
                
                found_anything = False
                
                for field, pats in patterns.items():
                    for pat in pats:
                        matches = re.findall(pat, all_text, re.IGNORECASE)
                        if matches:
                            print(f"  ✅ {field} pattern found: {matches[:5]}")  # Show first 5 matches
                            found_anything = True
                            break
                
                if not found_anything:
                    print("  ❌ No measurement patterns found")
                
                # Show some sample text
                print(f"\n📝 Sample text (first 500 chars):")
                print("-" * 30)
                print(all_text[:500])
                print("-" * 30)
                
            else:
                print("❌ No text could be extracted from this PDF")
                print("This PDF might be:")
                print("  • Image-based (scanned document)")
                print("  • Encrypted or password protected")
                print("  • Corrupted")
                
    except Exception as e:
        print(f"❌ Error reading PDF: {e}")

def debug_parts_loading():
    """Test parts.csv loading."""
    print("\n🔍 Parts Database Debug")
    print("=" * 50)
    
    csv_path = "parts.csv"
    
    try:
        # Check if file exists
        import os
        if not os.path.exists(csv_path):
            print(f"❌ parts.csv not found in current directory")
            print(f"Current directory: {os.getcwd()}")
            print("Files in directory:")
            for file in os.listdir("."):
                print(f"  • {file}")
            return
        
        print(f"✅ Found {csv_path}")
        
        # Load and examine the CSV
        df = pd.read_csv(csv_path)
        print(f"✅ CSV loaded successfully")
        print(f"📊 Rows: {len(df)}, Columns: {len(df.columns)}")
        print(f"📋 Columns: {list(df.columns)}")
        
        if "product" in df.columns:
            products = df["product"].unique()
            print(f"✅ Products found: {list(products)}")
            
            for product in products:
                product_parts = df[df["product"].str.lower() == product.lower()]
                print(f"  • {product}: {len(product_parts)} parts")
        else:
            print("❌ No 'product' column found")
        
        print(f"\n📄 First few rows:")
        print(df.head())
        
    except Exception as e:
        print(f"❌ Error loading parts.csv: {e}")

def main():
    print("🏠 Isaiah Industries - Debug Tool")
    print("🔧 Let's figure out what's going wrong!")
    print("\n")
    
    # Test parts loading first
    debug_parts_loading()
    
    # Then test PDF reading
    debug_pdf_reading()
    
    print("\n" + "=" * 50)
    print("🔧 Debug complete!")
    print("Check the output above to see what's working and what isn't.")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
