# 🏠 Isaiah Industries - Professional Roof Material Analysis Tool

[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](https://github.com/yourusername/isaiah-roof-tool)
[![Version](https://img.shields.io/badge/version-2.0-blue)](https://github.com/yourusername/isaiah-roof-tool/releases)
[![License](https://img.shields.io/badge/license-Commercial-orange)](LICENSE)

> **Commercial-grade roof material analysis tool for contractors**

Transform PDF roof reports into professional material lists instantly. Built for contractors who demand accuracy, efficiency, and professional presentation.

## ✨ **Key Features**

- 🔍 **Smart PDF Analysis** - Extracts measurements from EagleView, RoofSnap, Hover, Roofr, and more
- 🎨 **Modern Professional UI** - Commercial-grade interface ready for client presentations
- 📊 **Accurate Calculations** - Built-in waste factors and coverage rates
- 📄 **Branded Reports** - Professional PDF output with Isaiah Industries branding
- 💻 **Cross-Platform** - Windows and Mac versions available
- 🚫 **No Dependencies** - No OCR installation required

## 🚀 **Quick Start**

### **For End Users (Contractors)**
1. Download `Isaiah_Industries_Roof_Tool.exe` from [Releases](https://github.com/yourusername/isaiah-roof-tool/releases)
2. Double-click to run - no installation needed!
3. Upload your roof report PDF
4. Generate professional material list

### **For Developers**
```bash
git clone https://github.com/yourusername/isaiah-roof-tool.git
cd isaiah-roof-tool
pip install -r requirements.txt
python roof_material_tool.py
```

## 📋 **Supported Vendors**

| Vendor | Status | Notes |
|--------|--------|-------|
| EagleView | ✅ Full Support | Industry standard |
| RoofSnap | ✅ Full Support | Popular choice |
| Hover | ✅ Full Support | Mobile-friendly |
| Roofr | ✅ Full Support | Comprehensive reports |
| GAF QuickMeasure | ✅ Full Support | Professional grade |
| Xactimate | ✅ Full Support | Insurance standard |
| Generic PDFs | ✅ Manual Input | Any measurement PDF |

## 🛠️ **Development**

### **Building Executables**

**Windows:**
```bash
build.bat
```

**Mac:**
```bash
chmod +x build_mac.sh
./build_mac.sh
```

### **Testing**
```bash
python test_pdf_reading.py
python debug_pdf.py
python quick_test.py
```

## 📁 **Project Structure**

```
isaiah-roof-tool/
├── roof_material_tool.py          # Main Windows application
├── roof_material_tool_mac.py      # Mac-optimized version
├── parts.csv                      # Product database
├── requirements.txt               # Dependencies
├── build.bat                      # Windows build script
├── build_mac.sh                   # Mac build script
├── tests/                         # Test files
│   ├── debug_pdf.py
│   ├── test_pdf_reading.py
│   └── quick_test.py
├── docs/                          # Documentation
│   ├── README_FOR_COWORKERS.md
│   ├── MAC_SETUP_INSTRUCTIONS.md
│   └── COMMERCIAL_README.md
└── .github/                       # GitHub workflows
    └── workflows/
        ├── build.yml
        └── test.yml
```

## 🎯 **Roadmap**

### **v2.1 - Enhanced PDF Processing**
- [ ] Improved text extraction algorithms
- [ ] Support for encrypted PDFs
- [ ] Better table recognition
- [ ] Enhanced vendor detection

### **v2.2 - UI/UX Improvements**
- [ ] Progress indicators
- [ ] Drag-and-drop PDF upload
- [ ] Settings panel
- [ ] Custom part databases

### **v2.3 - Commercial Features**
- [ ] Batch processing
- [ ] Export to Excel
- [ ] Custom branding options
- [ ] User preferences

### **v3.0 - Platform Expansion**
- [ ] Web application
- [ ] Mobile app (iOS/Android)
- [ ] Cloud processing
- [ ] API for integrations

## 🤝 **Contributing**

We welcome contributions! This project uses AI agents for continuous development.

### **For AI Agents:**
- Check [Issues](https://github.com/yourusername/isaiah-roof-tool/issues) for tasks
- Follow coding standards in `.github/CONTRIBUTING.md`
- All PRs require tests and documentation

### **For Human Contributors:**
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 🐛 **Bug Reports & Feature Requests**

- **Bug Reports**: [Create an Issue](https://github.com/yourusername/isaiah-roof-tool/issues/new?template=bug_report.md)
- **Feature Requests**: [Create an Issue](https://github.com/yourusername/isaiah-roof-tool/issues/new?template=feature_request.md)
- **Questions**: [Discussions](https://github.com/yourusername/isaiah-roof-tool/discussions)

## 📊 **Commercial Use**

This tool is designed for commercial use by roofing contractors. For licensing and volume pricing:

- **Contact**: Isaiah Industries
- **Website**: [Isaiah Industries](https://www.isaiahind.com)
- **Support**: Commercial support available

## 📄 **License**

Commercial License - See [LICENSE](LICENSE) for details.

## 🏆 **Why Choose This Tool?**

✅ **Proven Technology** - Built on reliable PDF processing libraries  
✅ **Industry Focused** - Designed specifically for roofing contractors  
✅ **Professional Grade** - Commercial-quality interface and output  
✅ **Easy Deployment** - Single file, minimal setup required  
✅ **Continuous Updates** - AI-powered development for rapid improvements  

---

**© 2024 Isaiah Industries - Professional Roofing Solutions**
