"""
Test the window layout and button visibility
"""
import sys

def test_layout():
    """Test the window layout with scrolling."""
    print("🧪 Testing Window Layout and Button Visibility")
    print("=" * 50)
    
    try:
        # Test running the actual GUI
        print("🖥️ Testing the actual GUI...")
        print("This will open the real application window.")
        print("Look for:")
        print("  • Product buttons at the top")
        print("  • A red TEST BUTTON in the middle")
        print("  • The main gray UPLOAD button below that")
        print("  • Scrollbar on the right if needed")
        
        # Import and run the GUI
        sys.path.append('.')
        from roof_material_tool import run_gui
        
        print(f"\n🚀 Opening GUI...")
        print(f"If you see the red TEST BUTTON, the layout is working!")
        
        run_gui()
        
        return True
        
    except Exception as e:
        print(f"❌ Layout test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the layout test."""
    print("🏠 Isaiah Industries - Window Layout Test")
    print("🔍 Testing if the button visibility issue is fixed")
    
    print(f"\n💡 Changes made:")
    print("• Window height increased from 650px to 800px")
    print("• Added scrollable canvas for all content")
    print("• Made window resizable")
    print("• Added red TEST BUTTON for debugging")
    print("• Simplified button layout")
    
    print(f"\n🎯 What to look for:")
    print("1. Window should be taller (800px)")
    print("2. Should see product buttons at top")
    print("3. Should see red TEST BUTTON in middle")
    print("4. Should see gray UPLOAD button below that")
    print("5. Should be able to scroll if needed")
    
    input("\nPress Enter to open the GUI test...")
    
    test_result = test_layout()
    
    print(f"\n🎯 TEST RESULT: {'PASS' if test_result else 'FAIL'}")
    
    if test_result:
        print(f"\n🎉 Layout test completed!")
        print(f"If you saw all the buttons, the visibility issue is fixed!")
    else:
        print(f"\n❌ Layout test failed!")
        print(f"Check the error messages above.")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
