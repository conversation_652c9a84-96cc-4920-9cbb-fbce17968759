"""
Test the filename cleaning fix for the PDF generation error
"""
import re
import datetime

def test_filename_cleaning():
    """Test the filename cleaning logic."""
    print("🧪 Testing Filename Cleaning Fix")
    print("=" * 40)
    
    # Test various product names that might cause issues
    test_products = [
        "Country Manor Shake",
        "Oxford Shingle",
        "Product with Special!@#$%^&*()Characters",
        "Product   with   multiple   spaces",
        "Product-with-dashes",
        "Product_with_underscores",
        "Product/with\\slashes",
        "Product:with;colons",
        "Product\"with'quotes",
        "",  # Empty string
        "   ",  # Just spaces
        "!@#$%^&*()",  # Only special characters
    ]
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    print(f"📋 Testing filename cleaning for various product names:")
    print(f"🕐 Timestamp: {timestamp}")
    
    for product in test_products:
        print(f"\n🔍 Testing: '{product}'")
        
        # Apply the same cleaning logic as the main app
        safe_product_name = re.sub(r'[^\w\s-]', '', product)  # Remove special chars
        safe_product_name = re.sub(r'\s+', '_', safe_product_name)  # Replace spaces with underscores
        safe_product_name = safe_product_name.strip('_')  # Remove leading/trailing underscores
        
        if not safe_product_name:  # Fallback if product name becomes empty
            safe_product_name = "Material_List"
        
        suggested_filename = f"{safe_product_name}_{timestamp}.pdf"
        
        print(f"  ✅ Cleaned: '{safe_product_name}'")
        print(f"  📄 Filename: '{suggested_filename}'")
        
        # Check if filename is safe for Windows
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\']
        has_invalid = any(char in suggested_filename for char in invalid_chars)
        
        if has_invalid:
            print(f"  ❌ Still has invalid characters!")
        else:
            print(f"  ✅ Filename is safe")
    
    return True

def test_file_dialog_parameters():
    """Test the file dialog parameters."""
    print(f"\n🖥️ Testing File Dialog Parameters")
    print("=" * 35)
    
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    product = "Country Manor Shake"
    
    # Test the cleaning
    safe_product_name = re.sub(r'[^\w\s-]', '', product)
    safe_product_name = re.sub(r'\s+', '_', safe_product_name)
    safe_product_name = safe_product_name.strip('_')
    
    if not safe_product_name:
        safe_product_name = "Material_List"
    
    suggested_filename = f"{safe_product_name}_{timestamp}.pdf"
    
    print(f"📋 Product: '{product}'")
    print(f"📄 Suggested filename: '{suggested_filename}'")
    
    # Test dialog parameters
    dialog_params = {
        "title": "Save Material List PDF - Choose Your Folder",
        "defaultextension": ".pdf",
        "filetypes": [("PDF Files", "*.pdf"), ("All Files", "*.*")],
        "initialname": suggested_filename,
        "initialdir": "~/Desktop"
    }
    
    print(f"\n🔧 Dialog parameters:")
    for key, value in dialog_params.items():
        print(f"  • {key}: {value}")
    
    # Check for potential issues
    issues = []
    
    if len(suggested_filename) > 255:
        issues.append("Filename too long")
    
    if not suggested_filename.endswith('.pdf'):
        issues.append("Missing .pdf extension")
    
    if any(char in suggested_filename for char in ['<', '>', ':', '"', '|', '?', '*']):
        issues.append("Contains invalid characters")
    
    if issues:
        print(f"\n❌ Potential issues found:")
        for issue in issues:
            print(f"  • {issue}")
    else:
        print(f"\n✅ All parameters look good!")
    
    return len(issues) == 0

def main():
    """Test the filename fix."""
    print("🏠 Isaiah Industries - Filename Fix Test")
    print("🔧 Testing the fix for PDF generation filename errors")
    
    print(f"\n💡 The error was likely caused by:")
    print("• Special characters in product names")
    print("• Spaces not properly handled")
    print("• Invalid filename characters")
    print("• File dialog parameter issues")
    
    print(f"\n🔧 The fix includes:")
    print("• Better regex cleaning of product names")
    print("• Proper space-to-underscore conversion")
    print("• Fallback for empty product names")
    print("• Exception handling for dialog errors")
    print("• Simpler fallback filename")
    
    filename_test = test_filename_cleaning()
    dialog_test = test_file_dialog_parameters()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print(f"✅ Filename Cleaning: {'PASS' if filename_test else 'FAIL'}")
    print(f"✅ Dialog Parameters: {'PASS' if dialog_test else 'FAIL'}")
    
    if filename_test and dialog_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The filename fix should resolve the PDF generation error.")
        print(f"\n💡 Key improvements:")
        print("• Safer filename generation")
        print("• Better special character handling")
        print("• Fallback mechanisms")
        print("• Exception handling")
    else:
        print(f"\n⚠️ Some tests failed - check issues above")
    
    print(f"\n🚀 Try the tool again:")
    print("1. Rebuild: build.bat")
    print("2. Test: python roof_material_tool.py")
    print("3. The PDF generation error should be fixed!")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
