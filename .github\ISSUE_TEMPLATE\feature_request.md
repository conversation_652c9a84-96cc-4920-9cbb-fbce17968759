---
name: Feature Request
about: Suggest an idea for the Isaiah Industries Roof Tool
title: '[FEATURE] '
labels: enhancement
assignees: ''

---

## 🚀 **Feature Description**
A clear and concise description of what you want to happen.

## 💡 **Problem/Use Case**
Is your feature request related to a problem? Please describe.
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

## 🎯 **Proposed Solution**
Describe the solution you'd like.
A clear and concise description of what you want to happen.

## 🔄 **Alternative Solutions**
Describe alternatives you've considered.
A clear and concise description of any alternative solutions or features you've considered.

## 👥 **Target Users**
Who would benefit from this feature?
- [ ] Roofing contractors
- [ ] Sales teams
- [ ] Project managers
- [ ] Estimators
- [ ] Other: ___________

## 📊 **Business Impact**
How would this feature impact the business?
- [ ] Increase efficiency
- [ ] Reduce errors
- [ ] Improve user experience
- [ ] Enable new use cases
- [ ] Competitive advantage
- [ ] Other: ___________

## 🔧 **Technical Considerations**
Any technical requirements or constraints?
- **Platform**: [Windows/Mac/Both]
- **Integration**: [PDF vendors, export formats, etc.]
- **Performance**: [Speed, memory, file size requirements]

## 📋 **Acceptance Criteria**
What would make this feature complete?
- [ ] Criterion 1
- [ ] Criterion 2
- [ ] Criterion 3

## 📸 **Mockups/Examples** (optional)
If applicable, add mockups, screenshots, or examples to help explain your feature request.

## 🎯 **Priority**
How important is this feature?
- [ ] Critical (blocks current work)
- [ ] High (significantly improves workflow)
- [ ] Medium (nice to have improvement)
- [ ] Low (minor enhancement)

---

**For AI Agents**: This feature request is ready for automated development. Please:
1. Analyze the requirements
2. Design the implementation approach
3. Break down into smaller tasks
4. Implement with tests
5. Update documentation
6. Consider cross-platform compatibility
