# 🍎 Isaiah Industries Roof Tool - Mac Version Setup

## 📋 **Prerequisites for Mac**

### **1. Install Python 3**
```bash
# Using Homebrew (recommended)
brew install python

# Or download from python.org
# https://www.python.org/downloads/macos/
```

### **2. Install Tesseract OCR**
```bash
# Using Homebrew (easiest)
brew install tesseract

# Or download from GitHub
# https://github.com/UB-Mannheim/tesseract/wiki
```

### **3. Install Required Python Packages**
```bash
pip3 install -r requirements.txt
```

---

## 🚀 **Building the Mac App**

### **Option 1: Automated Build (Recommended)**
```bash
# Make the script executable
chmod +x build_mac.sh

# Run the build script
./build_mac.sh
```

### **Option 2: Manual Build**
```bash
# Install dependencies
pip3 install pyinstaller pdfplumber pandas fpdf2 pdf2image pytesseract Pillow

# Build the app
pyinstaller --onefile --windowed --name "Isaiah_Industries_Roof_Tool" --add-data "parts.csv:." roof_material_tool_mac.py
```

---

## 📁 **What You Get**

After building, you'll find:
- **`dist/Isaiah_Industries_Roof_Tool`** - The Mac application
- Ready to distribute to Mac contractors
- No installation required on target Macs (except Tesseract)

---

## 🎯 **Mac-Specific Features**

### **Native Mac Integration:**
- ✅ Uses SF Pro Display font (Apple's system font)
- ✅ Mac-style file dialogs
- ✅ Proper macOS error messages
- ✅ Optimized for Mac UI guidelines

### **Installation Instructions for Mac Contractors:**

1. **Install Tesseract OCR:**
   ```bash
   # If they have Homebrew
   brew install tesseract
   
   # Or download installer from GitHub
   ```

2. **Copy the App:**
   - Copy `Isaiah_Industries_Roof_Tool` to Applications folder
   - Or run from any location

3. **First Run:**
   - Right-click the app and select "Open" (for security)
   - macOS will ask for permission - click "Open"
   - Subsequent runs can be done normally

---

## 🔧 **Troubleshooting Mac Issues**

### **"App can't be opened" error:**
```bash
# Remove quarantine attribute
xattr -d com.apple.quarantine Isaiah_Industries_Roof_Tool
```

### **Tesseract not found:**
```bash
# Check if installed
which tesseract

# Install if missing
brew install tesseract
```

### **Permission issues:**
```bash
# Make executable
chmod +x Isaiah_Industries_Roof_Tool
```

---

## 📦 **Distribution to Mac Contractors**

### **What to Send:**
1. **The App File**: `Isaiah_Industries_Roof_Tool`
2. **Setup Instructions**: This document
3. **Tesseract Installer**: Link or instructions

### **Simple Instructions for Contractors:**
1. Install Tesseract: `brew install tesseract`
2. Copy the app to Applications
3. Right-click and "Open" the first time
4. Ready to use!

---

## 🆚 **Mac vs Windows Differences**

| Feature | Mac Version | Windows Version |
|---------|-------------|-----------------|
| **Font** | SF Pro Display | Segoe UI |
| **File Extension** | No extension | .exe |
| **Installation** | Copy to Applications | Copy anywhere |
| **Tesseract Install** | `brew install tesseract` | Download installer |
| **First Run** | Right-click "Open" | Double-click |

---

## 🏆 **Commercial Benefits for Mac**

- ✅ **Native Mac Look**: Feels like a professional Mac app
- ✅ **Easy Distribution**: Single file, no installer needed
- ✅ **Professional Appearance**: Perfect for Mac-using contractors
- ✅ **Same Functionality**: All Windows features work on Mac

---

## 📞 **Support**

### **For Build Issues:**
- Ensure Python 3 and pip3 are installed
- Check that all dependencies install correctly
- Verify Tesseract is in PATH

### **For Distribution:**
- Test on a clean Mac first
- Provide clear Tesseract installation instructions
- Include this setup guide with the app

---

*Ready to serve Mac contractors with professional roofing tools!*

**© 2024 Isaiah Industries - Cross-Platform Professional Solutions**
