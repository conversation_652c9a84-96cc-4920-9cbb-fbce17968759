"""
Test button visibility in the interface
"""
import sys
import tkinter as tk

def test_button_visibility():
    """Test that all buttons are visible."""
    print("🧪 Testing Button Visibility")
    print("=" * 30)
    
    try:
        sys.path.append('.')
        from roof_material_tool import COLORS, PRODUCTS, darken_color, create_card_frame
        
        # Create test window
        root = tk.Tk()
        root.title("Button Visibility Test")
        root.geometry("800x600")
        root.configure(bg=COLORS['background'])
        
        main_container = tk.Frame(root, bg=COLORS['background'])
        main_container.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Test product buttons
        print("🔘 Testing product buttons...")
        product_card = create_card_frame(main_container)
        product_card.pack(fill='x', pady=(0, 20))
        
        product_content = tk.Frame(product_card, bg=COLORS['card'])
        product_content.pack(fill='x', padx=30, pady=25)
        
        tk.Label(
            product_content,
            text="Step 1: Select Your Product Line",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['card']
        ).pack(pady=(0, 15))
        
        # Product buttons
        selected_product = tk.StringVar(value="")
        
        def select_product(product_name):
            selected_product.set(product_name)
            print(f"Selected: {product_name}")
            # Enable main button
            main_btn.config(
                state='normal',
                bg=COLORS['secondary'],
                fg='white',
                cursor='hand2'
            )
            status_label.config(text=f"✅ Selected: {product_name} - Main button should now be enabled!", fg=COLORS['success'])
        
        for product in PRODUCTS:
            btn = tk.Button(
                product_content,
                text=product,
                command=lambda p=product: select_product(p),
                font=('Segoe UI', 12, 'bold'),
                bg=COLORS['secondary'],
                fg='white',
                relief='flat',
                padx=20,
                pady=15,
                width=25
            )
            btn.pack(pady=5)
            print(f"  ✅ Created button: {product}")
        
        # Status
        status_card = create_card_frame(main_container)
        status_card.pack(fill='x', pady=(0, 20))
        
        status_content = tk.Frame(status_card, bg=COLORS['card'])
        status_content.pack(fill='x', padx=30, pady=20)
        
        status_label = tk.Label(
            status_content,
            text="👆 Please select a product line above",
            font=('Segoe UI', 11),
            fg=COLORS['warning'],
            bg=COLORS['card']
        )
        status_label.pack()
        
        # Main action button
        print("📄 Testing main action button...")
        action_card = create_card_frame(main_container)
        action_card.pack(fill='x', pady=(0, 20))
        
        action_content = tk.Frame(action_card, bg=COLORS['card'])
        action_content.pack(fill='x', padx=40, pady=40)
        
        tk.Label(
            action_content,
            text="Step 2: Upload Your PDF",
            font=('Segoe UI', 14, 'bold'),
            fg=COLORS['text_primary'],
            bg=COLORS['card']
        ).pack(pady=(0, 15))
        
        # Button frame
        button_frame = tk.Frame(action_content, bg=COLORS['card'])
        button_frame.pack(pady=20)
        
        # Shadow button
        shadow_btn = tk.Button(
            button_frame,
            text="📄 UPLOAD & ANALYZE ROOF REPORT",
            font=('Segoe UI', 16, 'bold'),
            bg='#AAAAAA',
            fg='#AAAAAA',
            relief='flat',
            borderwidth=0,
            padx=52,
            pady=27,
            width=40,
            height=4,
            state='disabled'
        )
        shadow_btn.pack()
        print("  ✅ Created shadow button")
        
        # Main button
        def test_click():
            product = selected_product.get()
            if product:
                tk.messagebox.showinfo("Test", f"Button clicked! Selected product: {product}")
            else:
                tk.messagebox.showwarning("Test", "No product selected")
        
        main_btn = tk.Button(
            button_frame,
            text="📄 UPLOAD & ANALYZE ROOF REPORT",
            command=test_click,
            font=('Segoe UI', 16, 'bold'),
            bg='#CCCCCC',  # Light gray but visible
            fg='#666666',  # Dark gray text
            relief='flat',
            borderwidth=2,
            cursor='arrow',
            padx=50,
            pady=25,
            width=40,
            height=4,
            state='disabled'
        )
        main_btn.place(in_=shadow_btn, x=-2, y=-2)
        print("  ✅ Created main button (disabled)")
        
        # Help text
        tk.Label(
            action_content,
            text="💡 Select a product above to enable this button",
            font=('Segoe UI', 11, 'italic'),
            fg=COLORS['warning'],
            bg=COLORS['card']
        ).pack(pady=(15, 0))
        
        # Test button to close
        test_btn = tk.Button(
            action_content,
            text="Close Test",
            command=root.destroy,
            font=('Segoe UI', 10),
            bg=COLORS['accent'],
            fg='white',
            padx=15,
            pady=5
        )
        test_btn.pack(pady=10)
        
        print(f"\n✅ All buttons created successfully!")
        print(f"🖱️ Test the interface:")
        print(f"  1. Click a product button (should turn green)")
        print(f"  2. Main button should become enabled (blue)")
        print(f"  3. Click main button to test functionality")
        
        # Run the test
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Button visibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run button visibility test."""
    print("🏠 Isaiah Industries - Button Visibility Test")
    print("🔍 Testing that all buttons are visible and working")
    
    test_result = test_button_visibility()
    
    print(f"\n🎯 TEST RESULT: {'PASS' if test_result else 'FAIL'}")
    
    if test_result:
        print(f"\n🎉 Button visibility test completed!")
        print(f"If you saw all the buttons in the test window, the interface is working.")
    else:
        print(f"\n❌ Button visibility test failed!")
        print(f"Check the error messages above.")
    
    print(f"\n💡 Expected behavior:")
    print("• Product buttons should be visible and clickable")
    print("• Main button should be visible but disabled (gray)")
    print("• After selecting product, main button should become blue")
    print("• All buttons should have proper hover effects")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
