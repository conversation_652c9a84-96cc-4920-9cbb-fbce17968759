"""
Test that PDFs are actually being created and saved
"""
import sys
import os
import tempfile

def test_pdf_creation():
    """Test the PDF creation and saving process."""
    print("🧪 Testing PDF Creation and Saving")
    print("=" * 40)
    
    try:
        sys.path.append('.')
        from roof_material_tool import MaterialListPDF, calculate_materials, load_parts
        import pandas as pd
        
        # Create test data
        test_data = {
            "vendor": "test",
            "total_sqft": 2000,
            "ridge_length": 50,
            "hip_length": 30,
            "valley_length": 20,
            "eaves_length": 150
        }
        
        print(f"📋 Test data:")
        for key, value in test_data.items():
            print(f"  • {key}: {value}")
        
        # Load parts and calculate materials
        print(f"\n🔧 Loading parts for Country Manor Shake...")
        parts = load_parts("Country Manor Shake")
        
        if not parts:
            print(f"❌ No parts loaded")
            return False
        
        print(f"✅ Loaded {len(parts)} parts")
        
        # Calculate materials
        print(f"\n🧮 Calculating materials...")
        results = calculate_materials(test_data, parts)
        
        if not results:
            print(f"❌ No materials calculated")
            return False
        
        print(f"✅ Calculated {len(results)} material items")
        for result in results[:3]:  # Show first 3
            print(f"  • {result['description']}: {result['quantity']:.1f} {result['unit']}")
        
        # Test PDF creation
        print(f"\n📄 Creating PDF...")
        
        # Create temporary file for testing
        temp_dir = tempfile.gettempdir()
        test_pdf_path = os.path.join(temp_dir, "test_material_list.pdf")
        
        print(f"📁 Test PDF path: {test_pdf_path}")
        
        # Create PDF step by step (same as main app)
        pdf = MaterialListPDF()
        pdf.add_page()
        
        # Set font
        try:
            pdf.set_font("Arial", "", 10)
            print(f"✅ Font set successfully")
        except Exception as font_error:
            print(f"❌ Font error: {font_error}")
            pdf.set_font("Arial", "", 12)  # Fallback
        
        # Add header
        try:
            pdf.cell(0, 10, f"Vendor: {test_data.get('vendor', 'unknown')}", ln=1)
            pdf.cell(0, 10, f"Product: Country Manor Shake", ln=1)
            pdf.cell(0, 10, f"Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
            pdf.cell(0, 10, f"Test PDF Generation", ln=1)
            pdf.ln(5)
            print(f"✅ Header added successfully")
        except Exception as header_error:
            print(f"❌ Header error: {header_error}")
            pdf.cell(0, 10, "Material List Report", ln=1)
            pdf.ln(5)
        
        # Add table
        try:
            if results and len(results) > 0:
                pdf.add_table(results)
                print(f"✅ Table added successfully")
            else:
                pdf.cell(0, 10, "No materials calculated", ln=1)
                print(f"⚠️ No materials to add to table")
        except Exception as table_error:
            print(f"❌ Table error: {table_error}")
            pdf.cell(0, 10, f"Error creating table: {str(table_error)}", ln=1)
        
        # Save PDF with multiple methods
        print(f"\n💾 Testing PDF save methods...")
        
        saved = False
        
        # Method 1: Simple output with 'F' flag
        try:
            pdf.output(test_pdf_path, 'F')
            if os.path.exists(test_pdf_path) and os.path.getsize(test_pdf_path) > 1000:
                print(f"✅ Method 1 successful: pdf.output(path, 'F')")
                saved = True
            else:
                print(f"❌ Method 1 failed: file not created or too small")
        except Exception as e:
            print(f"❌ Method 1 error: {e}")
        
        # Method 2: Simple output without flag
        if not saved:
            try:
                test_pdf_path2 = test_pdf_path.replace('.pdf', '_method2.pdf')
                pdf.output(test_pdf_path2)
                if os.path.exists(test_pdf_path2) and os.path.getsize(test_pdf_path2) > 1000:
                    print(f"✅ Method 2 successful: pdf.output(path)")
                    test_pdf_path = test_pdf_path2
                    saved = True
                else:
                    print(f"❌ Method 2 failed: file not created or too small")
            except Exception as e:
                print(f"❌ Method 2 error: {e}")
        
        # Method 3: String output
        if not saved:
            try:
                test_pdf_path3 = test_pdf_path.replace('.pdf', '_method3.pdf')
                pdf_string = pdf.output(dest='S')
                with open(test_pdf_path3, 'wb') as f:
                    if isinstance(pdf_string, str):
                        f.write(pdf_string.encode('latin1'))
                    else:
                        f.write(pdf_string)
                
                if os.path.exists(test_pdf_path3) and os.path.getsize(test_pdf_path3) > 1000:
                    print(f"✅ Method 3 successful: string output")
                    test_pdf_path = test_pdf_path3
                    saved = True
                else:
                    print(f"❌ Method 3 failed: file not created or too small")
            except Exception as e:
                print(f"❌ Method 3 error: {e}")
        
        if saved:
            file_size = os.path.getsize(test_pdf_path)
            print(f"\n🎉 PDF CREATED SUCCESSFULLY!")
            print(f"📄 File: {os.path.basename(test_pdf_path)}")
            print(f"📁 Location: {os.path.dirname(test_pdf_path)}")
            print(f"📊 Size: {file_size:,} bytes")
            
            # Clean up test file
            try:
                os.unlink(test_pdf_path)
                print(f"🧹 Test file cleaned up")
            except:
                print(f"⚠️ Could not clean up test file")
            
            return True
        else:
            print(f"\n❌ ALL PDF SAVE METHODS FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ PDF creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Test PDF saving functionality."""
    print("🏠 Isaiah Industries - PDF Saving Test")
    print("🔍 Testing if PDFs are actually being created and saved")
    
    print(f"\n💡 The issue was:")
    print("• PDF saving code was inside the table error block")
    print("• It only ran if there was a table error")
    print("• If table creation succeeded, PDF was never saved!")
    
    print(f"\n🔧 The fix:")
    print("• Moved PDF saving code outside the table error block")
    print("• PDF now saves regardless of table success/failure")
    print("• Multiple save methods as fallbacks")
    
    test_result = test_pdf_creation()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULT:")
    print(f"✅ PDF Creation & Saving: {'PASS' if test_result else 'FAIL'}")
    
    if test_result:
        print(f"\n🎉 SUCCESS!")
        print(f"PDFs are now being created and saved properly!")
        print(f"\n💡 What was fixed:")
        print("• PDF saving code moved to correct location")
        print("• Multiple save methods for reliability")
        print("• Proper error handling")
        print("• File size verification")
        
        print(f"\n🚀 Ready to rebuild:")
        print("1. Run: build.bat")
        print("2. Test with real PDFs")
        print("3. PDFs should now actually save!")
    else:
        print(f"\n❌ Test failed!")
        print("Check the error messages above for details.")
    
    print(f"\n📋 The tool should now:")
    print("• Process your roof measurements")
    print("• Calculate realistic material quantities")
    print("• Actually create and save the PDF file")
    print("• Show success dialog with file location")
    print("• Allow you to open the folder to view the PDF")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
