import pdfplumber
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
from fpdf import FPDF
import pandas as pd
import re
import math
import os
import platform
from typing import List, Dict, Any, Optional
import threading
# OCR dependencies
from pdf2image import convert_from_path
import pytesseract

# Modern UI Colors and Styling
COLORS = {
    'primary': '#2C3E50',      # Dark blue-gray
    'secondary': '#3498DB',     # Bright blue
    'accent': '#E74C3C',       # Red accent
    'success': '#27AE60',      # Green
    'warning': '#F39C12',      # Orange
    'background': '#ECF0F1',   # Light gray
    'card': '#FFFFFF',         # White
    'text_primary': '#2C3E50', # Dark text
    'text_secondary': '#7F8C8D', # Gray text
    'border': '#BDC3C7'        # Light border
}

CSV_PATH = "parts.csv"
COMPANY_NAME = "Isaiah Industries"
PRODUCTS = ["Country Manor Shake", "Oxford Shingle"]

def load_parts(product_name: str, csv_path: str = CSV_PATH) -> List[Dict[str, Any]]:
    """Load parts for a given product from CSV."""
    try:
        df = pd.read_csv(csv_path)
        if "product" not in df.columns:
            raise ValueError("CSV missing 'product' column.")
        return df[df["product"].str.lower() == product_name.lower()].to_dict(orient="records")
    except Exception as e:
        messagebox.showerror("CSV Error", f"Failed to load parts: {e}")
        return []

def check_tesseract_installation() -> bool:
    """Check if Tesseract OCR is properly installed."""
    try:
        # Try to get Tesseract version
        version = pytesseract.get_tesseract_version()
        return True
    except Exception:
        return False

def extract_roof_data(path: str, progress_callback=None) -> Dict[str, Any]:
    """Detect vendor and extract roof data from PDF, using OCR fallback if needed."""
    if progress_callback:
        progress_callback("Reading PDF file...")
    
    text_all = ""
    
    # First try: Extract text directly from PDF
    try:
        with pdfplumber.open(path) as pdf:
            if progress_callback:
                progress_callback(f"Processing {len(pdf.pages)} pages...")
            
            for i, page in enumerate(pdf.pages):
                if progress_callback:
                    progress_callback(f"Reading page {i+1}/{len(pdf.pages)}...")
                page_text = page.extract_text() or ""
                text_all += page_text
                
    except Exception as e:
        error_msg = f"Failed to read PDF: {str(e)}"
        if progress_callback:
            progress_callback(f"Error: {error_msg}")
        messagebox.showerror("PDF Error", error_msg)
        return {}

    # If no text found, try OCR
    if not text_all.strip():
        if progress_callback:
            progress_callback("No text found in PDF, trying OCR...")
        
        # Check if Tesseract is installed
        if not check_tesseract_installation():
            if platform.system() == "Darwin":  # macOS
                error_msg = ("Tesseract OCR is not installed or not found in PATH.\n\n"
                            "Please install Tesseract OCR using Homebrew:\n"
                            "brew install tesseract\n\n"
                            "Or download from: https://github.com/UB-Mannheim/tesseract/wiki")
            else:
                error_msg = ("Tesseract OCR is not installed or not found in PATH.\n\n"
                            "Please install Tesseract OCR from:\n"
                            "https://github.com/UB-Mannheim/tesseract/wiki\n\n"
                            "After installation, restart your computer.")
            messagebox.showerror("OCR Error", error_msg)
            return {}
        
        try:
            if progress_callback:
                progress_callback("Converting PDF to images...")
            
            # Convert PDF to images with better settings
            images = convert_from_path(
                path, 
                dpi=300,  # Higher DPI for better OCR
                first_page=1,
                last_page=5  # Limit to first 5 pages to avoid timeout
            )
            
            ocr_text = ""
            for i, img in enumerate(images):
                if progress_callback:
                    progress_callback(f"OCR processing image {i+1}/{len(images)}...")
                
                # Use better OCR configuration
                custom_config = r'--oem 3 --psm 6'
                page_text = pytesseract.image_to_string(img, config=custom_config)
                ocr_text += page_text + "\n"
            
            text_all = ocr_text
            
        except Exception as e:
            if platform.system() == "Darwin":  # macOS
                error_msg = f"OCR processing failed: {str(e)}\n\nPossible solutions:\n1. Install Tesseract: brew install tesseract\n2. Try a different PDF file"
            else:
                error_msg = f"OCR processing failed: {str(e)}\n\nPossible solutions:\n1. Install Tesseract OCR\n2. Restart your computer\n3. Try a different PDF file"
            if progress_callback:
                progress_callback(f"OCR Error: {str(e)}")
            messagebox.showerror("OCR Error", error_msg)
            return {}

    if progress_callback:
        progress_callback("Analyzing extracted text...")

    # Detect vendor
    vendor = "unknown"
    if "EagleView" in text_all: vendor = "EagleView"
    elif "RoofSnap" in text_all: vendor = "RoofSnap"
    elif "Hover" in text_all: vendor = "Hover"
    elif "Roofr" in text_all or "Roof Sketch" in text_all: vendor = "Roofr"

    # Extract measurements with improved patterns
    patterns = {
        "total_sqft": [
            r"Total\s+Area[:=]?\s*([\d,]+)",
            r"Roof\s+Area[:=]?\s*([\d,]+)",
            r"Total\s+Square\s+Feet[:=]?\s*([\d,]+)",
            r"(\d{1,5})\s*sq\.?\s*ft\.?\s*total",
            r"(\d{1,5})\s*square\s+feet"
        ],
        "ridge_length": [
            r"Ridges?[:=]?\s*([\d,]+)\s*ft",
            r"Ridge\s+Length[:=]?\s*([\d,]+)",
            r"Ridge[:=]?\s*([\d,]+)"
        ],
        "hip_length": [
            r"Hips?[:=]?\s*([\d,]+)\s*ft",
            r"Hip\s+Length[:=]?\s*([\d,]+)"
        ],
        "valley_length": [
            r"Valleys?[:=]?\s*([\d,]+)\s*ft",
            r"Valley\s+Length[:=]?\s*([\d,]+)"
        ],
        "eaves_length": [
            r"Eaves?[:=]?\s*([\d,]+)\s*ft",
            r"Eave\s+Length[:=]?\s*([\d,]+)",
            r"Gutter\s+Length[:=]?\s*([\d,]+)"
        ],
    }

    result = {"vendor": vendor, "extracted_text_length": len(text_all)}
    
    for field, pats in patterns.items():
        for pat in pats:
            match = re.search(pat, text_all, re.IGNORECASE)
            if match:
                try:
                    result[field] = int(match.group(1).replace(",", ""))
                    break
                except (ValueError, IndexError):
                    continue
        if field not in result:
            result[field] = None
    
    if progress_callback:
        progress_callback("Data extraction complete!")
    
    return result

def prompt_for_missing(data: Dict[str, Any]) -> Dict[str, Any]:
    """Prompt user for any missing data."""
    for k, v in data.items():
        if k != "vendor" and k != "extracted_text_length" and (v is None or v == 0):
            ans = simpledialog.askinteger("Missing Info", f"Enter {k.replace('_',' ')} (in feet):")
            data[k] = ans if ans else 0
    return data

def calculate_materials(data: Dict[str, Any], parts: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Calculate materials based on parts list and extracted data."""
    results = []
    for row in parts:
        src_val = data.get(row.get("source_metric", ""), 0)
        try:
            coverage = float(row.get("coverage", 1))
            waste = float(row.get("waste", 0))
        except (TypeError, ValueError):
            coverage, waste = 1, 0
        unit = row.get("unit", "pieces")
        count = math.ceil((src_val / coverage) * (1 + waste)) if coverage else 0
        results.append({
            "part_number": row.get("part_number", ""),
            "description": row.get("description", ""),
            "unit": unit,
            "count": count
        })
    return results

class MaterialListPDF(FPDF):
    """Professional PDF output for material list with Isaiah Industries branding."""
    def header(self):
        # Company header with professional styling
        self.set_fill_color(44, 62, 80)  # Dark blue background
        self.rect(0, 0, 210, 25, 'F')
        
        self.set_text_color(255, 255, 255)  # White text
        self.set_font("Arial", "B", 18)
        self.cell(0, 15, f"{COMPANY_NAME}", 0, 1, "C")
        
        self.set_text_color(200, 200, 200)  # Light gray
        self.set_font("Arial", "", 12)
        self.cell(0, 8, "Professional Roof Material Analysis", 0, 1, "C")
        
        self.ln(10)
        
        # Reset colors for body
        self.set_text_color(0, 0, 0)
        
    def footer(self):
        self.set_y(-15)
        self.set_font('Arial', 'I', 8)
        self.set_text_color(128, 128, 128)
        self.cell(0, 10, f'Generated by {COMPANY_NAME} - Page {self.page_no()}', 0, 0, 'C')

    def add_table(self, data: List[Dict[str, Any]]):
        # Table header with professional styling
        self.set_fill_color(52, 152, 219)  # Blue header
        self.set_text_color(255, 255, 255)  # White text
        self.set_font("Arial", "B", 11)
        
        col_width = [45, 80, 25, 25]
        headers = ["Part Number", "Description", "Unit", "Quantity"]
        
        for i, h in enumerate(headers):
            self.cell(col_width[i], 12, h, border=1, fill=True, align='C')
        self.ln()
        
        # Table rows with alternating colors
        self.set_text_color(0, 0, 0)  # Black text
        self.set_font("Arial", "", 10)
        
        for i, row in enumerate(data):
            # Alternate row colors
            if i % 2 == 0:
                self.set_fill_color(248, 249, 250)  # Light gray
            else:
                self.set_fill_color(255, 255, 255)  # White
            
            self.cell(col_width[0], 10, str(row.get("part_number", "")), border=1, fill=True)
            self.cell(col_width[1], 10, str(row.get("description", ""))[:35], border=1, fill=True)  # Truncate long descriptions
            self.cell(col_width[2], 10, str(row.get("unit", "")), border=1, fill=True, align='C')
            self.cell(col_width[3], 10, str(row.get("count", "")), border=1, fill=True, align='C')
            self.ln()
            
        # Add summary section
        self.ln(5)
        self.set_font("Arial", "B", 10)
        self.cell(0, 8, f"Total Items: {len(data)}", 0, 1)
        
        # Add note
        self.ln(5)
        self.set_font("Arial", "I", 9)
        self.set_text_color(100, 100, 100)
        self.cell(0, 6, "Note: Quantities include recommended waste factors. Verify measurements before ordering.", 0, 1)
        self.cell(0, 6, "For technical support, contact Isaiah Industries customer service.", 0, 1)

def create_modern_button(parent, text, command, bg_color=COLORS['secondary'], fg_color='white', width=None, height=None):
    """Create a modern-styled button."""
    # Use system font for Mac
    if platform.system() == "Darwin":  # macOS
        font_family = 'SF Pro Display'
    else:
        font_family = 'Segoe UI'
    
    btn = tk.Button(
        parent,
        text=text,
        command=command,
        bg=bg_color,
        fg=fg_color,
        font=(font_family, 10, 'bold'),
        relief='flat',
        borderwidth=0,
        cursor='hand2',
        padx=20,
        pady=10
    )
    if width:
        btn.config(width=width)
    if height:
        btn.config(height=height)
    
    # Hover effects
    def on_enter(_):
        btn.config(bg=darken_color(bg_color))
    def on_leave(_):
        btn.config(bg=bg_color)
    
    btn.bind("<Enter>", on_enter)
    btn.bind("<Leave>", on_leave)
    
    return btn

def darken_color(hex_color, factor=0.8):
    """Darken a hex color by a factor."""
    hex_color = hex_color.lstrip('#')
    rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    darkened = tuple(int(c * factor) for c in rgb)
    return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

def create_card_frame(parent, bg_color=COLORS['card']):
    """Create a modern card-style frame."""
    frame = tk.Frame(parent, bg=bg_color, relief='flat', bd=1)
    frame.config(highlightbackground=COLORS['border'], highlightthickness=1)
    return frame

def run_gui():
    """Main GUI loop with modern Isaiah Industries design."""
    root = tk.Tk()
    root.title(f"{COMPANY_NAME} - Roof Material Analysis Tool")
    root.geometry("700x500")
    root.configure(bg=COLORS['background'])
    root.resizable(True, False)
    
    # Configure modern font for Mac
    if platform.system() == "Darwin":  # macOS
        default_font = ('SF Pro Display', 10)
    else:
        default_font = ('Segoe UI', 10)
    root.option_add('*Font', default_font)

    # Main container with padding
    main_container = tk.Frame(root, bg=COLORS['background'])
    main_container.pack(fill='both', expand=True, padx=30, pady=20)
    
    # Header section
    header_card = create_card_frame(main_container)
    header_card.pack(fill='x', pady=(0, 20))
    
    header_content = tk.Frame(header_card, bg=COLORS['card'])
    header_content.pack(fill='x', padx=30, pady=25)
    
    # Company logo/title
    company_label = tk.Label(
        header_content, 
        text=f"{COMPANY_NAME}",
        font=(default_font[0], 24, 'bold'),
        fg=COLORS['primary'],
        bg=COLORS['card']
    )
    company_label.pack()
    
    subtitle_label = tk.Label(
        header_content,
        text="Professional Roof Material Analysis Tool",
        font=(default_font[0], 12),
        fg=COLORS['text_secondary'],
        bg=COLORS['card']
    )
    subtitle_label.pack(pady=(5, 0))
    
    # Product selection section
    product_card = create_card_frame(main_container)
    product_card.pack(fill='x', pady=(0, 20))
    
    product_content = tk.Frame(product_card, bg=COLORS['card'])
    product_content.pack(fill='x', padx=30, pady=20)
    
    product_label = tk.Label(
        product_content,
        text="Select Product Line:",
        font=(default_font[0], 12, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    )
    product_label.pack(anchor='w', pady=(0, 10))
    
    # Modern combobox styling
    style = ttk.Style()
    style.theme_use('clam')
    style.configure('Modern.TCombobox', 
                   fieldbackground=COLORS['background'],
                   background=COLORS['card'],
                   borderwidth=1,
                   relief='flat')
    
    prod_var = tk.StringVar(value=PRODUCTS[0])
    combo = ttk.Combobox(
        product_content, 
        textvariable=prod_var, 
        values=PRODUCTS, 
        state="readonly",
        style='Modern.TCombobox',
        font=(default_font[0], 11),
        width=40
    )
    combo.pack(anchor='w')

    # Status section
    status_card = create_card_frame(main_container)
    status_card.pack(fill='x', pady=(0, 20))
    
    status_content = tk.Frame(status_card, bg=COLORS['card'])
    status_content.pack(fill='x', padx=30, pady=15)
    
    status_var = tk.StringVar(value="Ready to process roof reports")
    status_label = tk.Label(
        status_content,
        textvariable=status_var,
        font=(default_font[0], 10),
        fg=COLORS['text_secondary'],
        bg=COLORS['card']
    )
    status_label.pack()

    def set_status(msg: str, color=COLORS['text_secondary']):
        status_var.set(msg)
        status_label.config(fg=color)
        root.update_idletasks()

    def generate():
        product = prod_var.get()
        path = filedialog.askopenfilename(
            title="Select Roof Report PDF",
            filetypes=[("PDF Files", "*.pdf")]
        )
        if not path:
            set_status("No PDF selected.")
            return

        # Update button state
        generate_btn.config(state="disabled", text="🔄 Processing...", bg=COLORS['warning'])
        set_status("Starting PDF analysis...", COLORS['secondary'])
        
        def process_pdf():
            """Process PDF in background thread to prevent GUI freezing."""
            try:
                # Extract data with progress updates
                def progress_callback(msg):
                    root.after(0, lambda: set_status(msg, COLORS['secondary']))
                
                data = extract_roof_data(path, progress_callback=progress_callback)
                
                if not data:
                    root.after(0, lambda: set_status("❌ Failed to extract roof data.", COLORS['accent']))
                    root.after(0, lambda: reset_button())
                    return

                # Show extraction results
                if data.get('extracted_text_length', 0) > 0:
                    success_msg = f"✅ Extracted {data['extracted_text_length']} characters. Vendor: {data.get('vendor', 'unknown')}"
                    root.after(0, lambda: set_status(success_msg, COLORS['success']))
                
                # Prompt for missing data (must be done on main thread)
                root.after(0, lambda: continue_processing(data, product))
                
            except Exception as e:
                error_msg = f"❌ Error processing PDF: {str(e)}"
                root.after(0, lambda: set_status(error_msg, COLORS['accent']))
                root.after(0, lambda: messagebox.showerror("Processing Error", error_msg))
                root.after(0, lambda: reset_button())

        def reset_button():
            """Reset button to original state."""
            generate_btn.config(
                state="normal", 
                text="📄 Analyze Roof Report", 
                bg=COLORS['secondary']
            )

        def continue_processing(data, product):
            """Continue processing after data extraction."""
            try:
                # Prompt for missing data
                set_status("📝 Please enter any missing measurements...", COLORS['warning'])
                data = prompt_for_missing(data)
                
                set_status("📋 Loading parts database...", COLORS['secondary'])
                parts = load_parts(product)
                if not parts:
                    set_status("❌ No parts found for selected product.", COLORS['accent'])
                    reset_button()
                    return

                set_status("🔢 Calculating material requirements...", COLORS['secondary'])
                results = calculate_materials(data, parts)

                save_path = filedialog.asksaveasfilename(
                    title="Save Material List - Isaiah Industries",
                    defaultextension=".pdf",
                    filetypes=[("PDF", "*.pdf")],
                    initialname=f"Material_List_{product.replace(' ', '_')}"
                )
                if not save_path:
                    set_status("💾 Save cancelled by user.", COLORS['warning'])
                    reset_button()
                    return

                set_status("📄 Generating professional PDF report...", COLORS['secondary'])
                pdf = MaterialListPDF()
                pdf.add_page()
                pdf.set_font("Arial", "", 10)
                pdf.cell(0, 10, f"Vendor Detected: {data.get('vendor', 'unknown')}", ln=1)
                pdf.cell(0, 10, f"Product Line: {product}", ln=1)
                pdf.cell(0, 10, f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
                pdf.cell(0, 10, f"Text Extracted: {data.get('extracted_text_length', 0)} characters", ln=1)
                pdf.ln(5)
                pdf.add_table(results)
                pdf.output(save_path)
                
                set_status("✅ Material list generated successfully!", COLORS['success'])
                messagebox.showinfo("Success - Isaiah Industries", 
                                  f"Professional material list saved to:\n{save_path}\n\nReady for contractor use!")
                
            except Exception as e:
                set_status("❌ Failed to generate PDF report.", COLORS['accent'])
                messagebox.showerror("PDF Generation Error", f"Failed to generate PDF: {e}")
            finally:
                reset_button()

        # Start processing in background thread
        thread = threading.Thread(target=process_pdf, daemon=True)
        thread.start()

    # Action section
    action_card = create_card_frame(main_container)
    action_card.pack(fill='x', pady=(0, 20))
    
    action_content = tk.Frame(action_card, bg=COLORS['card'])
    action_content.pack(fill='x', padx=30, pady=25)
    
    # Main action button with modern styling
    generate_btn = create_modern_button(
        action_content,
        text="📄 Analyze Roof Report",
        command=generate,
        bg_color=COLORS['secondary'],
        width=30,
        height=2
    )
    generate_btn.pack(pady=10)
    
    # Help text
    help_text = tk.Label(
        action_content,
        text="Upload PDF reports from EagleView, RoofSnap, Hover, Roofr, or any roof measurement PDF",
        font=(default_font[0], 9),
        fg=COLORS['text_secondary'],
        bg=COLORS['card'],
        wraplength=500
    )
    help_text.pack(pady=(5, 0))

    # Footer section
    footer_frame = tk.Frame(main_container, bg=COLORS['background'])
    footer_frame.pack(side="bottom", fill='x', pady=(20, 0))
    
    footer_content = tk.Frame(footer_frame, bg=COLORS['background'])
    footer_content.pack()
    
    # Company info
    tk.Label(
        footer_content, 
        text=f"© 2024 {COMPANY_NAME} - Professional Roofing Solutions",
        font=(default_font[0], 9),
        fg=COLORS['text_secondary'],
        bg=COLORS['background']
    ).pack()
    
    # Version info
    tk.Label(
        footer_content,
        text="Version 2.0 - Commercial Grade Tool (Mac)",
        font=(default_font[0], 8),
        fg=COLORS['text_secondary'],
        bg=COLORS['background']
    ).pack()

    root.mainloop()

if __name__ == "__main__":
    run_gui()
