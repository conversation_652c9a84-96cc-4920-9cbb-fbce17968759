"""
Test the UI improvements - especially the larger button
"""
import tkinter as tk
from tkinter import messagebox

# Modern UI Colors
COLORS = {
    'primary': '#2C3E50',
    'secondary': '#3498DB',
    'accent': '#E74C3C',
    'success': '#27AE60',
    'warning': '#F39C12',
    'background': '#ECF0F1',
    'card': '#FFFFFF',
    'text_primary': '#2C3E50',
    'text_secondary': '#7F8C8D',
    'border': '#BDC3C7'
}

def darken_color(hex_color, factor=0.8):
    """Darken a hex color by a factor."""
    hex_color = hex_color.lstrip('#')
    rgb = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
    darkened = tuple(int(c * factor) for c in rgb)
    return f"#{darkened[0]:02x}{darkened[1]:02x}{darkened[2]:02x}"

def create_card_frame(parent, bg_color=COLORS['card']):
    """Create a modern card-style frame."""
    frame = tk.Frame(parent, bg=bg_color, relief='flat', bd=1)
    frame.config(highlightbackground=COLORS['border'], highlightthickness=1)
    return frame

def test_button_clicked():
    """Test function for button click."""
    messagebox.showinfo("Button Test", "✅ The large button works perfectly!\n\nThis is much more prominent and user-friendly for contractors.")

def show_ui_comparison():
    """Show the improved UI with large button."""
    root = tk.Tk()
    root.title("Isaiah Industries - UI Improvement Test")
    root.geometry("800x650")
    root.configure(bg=COLORS['background'])
    
    # Main container
    main_container = tk.Frame(root, bg=COLORS['background'])
    main_container.pack(fill='both', expand=True, padx=30, pady=20)
    
    # Header
    header_card = create_card_frame(main_container)
    header_card.pack(fill='x', pady=(0, 20))
    
    header_content = tk.Frame(header_card, bg=COLORS['card'])
    header_content.pack(fill='x', padx=30, pady=25)
    
    tk.Label(
        header_content,
        text="Isaiah Industries",
        font=('Segoe UI', 24, 'bold'),
        fg=COLORS['primary'],
        bg=COLORS['card']
    ).pack()
    
    tk.Label(
        header_content,
        text="UI Improvement Test - Large Button Demo",
        font=('Segoe UI', 12),
        fg=COLORS['text_secondary'],
        bg=COLORS['card']
    ).pack(pady=(5, 0))
    
    # Comparison section
    comparison_card = create_card_frame(main_container)
    comparison_card.pack(fill='x', pady=(0, 20))
    
    comparison_content = tk.Frame(comparison_card, bg=COLORS['card'])
    comparison_content.pack(fill='x', padx=30, pady=20)
    
    tk.Label(
        comparison_content,
        text="🔄 Button Size Comparison",
        font=('Segoe UI', 14, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    ).pack(pady=(0, 15))
    
    # Old small button example
    old_frame = tk.Frame(comparison_content, bg=COLORS['card'])
    old_frame.pack(pady=10)
    
    tk.Label(old_frame, text="❌ Old Small Button:", font=('Segoe UI', 10, 'bold'), 
             fg=COLORS['accent'], bg=COLORS['card']).pack()
    
    small_btn = tk.Button(
        old_frame,
        text="📄 Analyze Roof Report",
        font=('Segoe UI', 10, 'bold'),
        bg=COLORS['secondary'],
        fg='white',
        relief='flat',
        padx=20,
        pady=10,
        width=30,
        height=2
    )
    small_btn.pack(pady=5)
    
    # New large button
    new_frame = tk.Frame(comparison_content, bg=COLORS['card'])
    new_frame.pack(pady=20)
    
    tk.Label(new_frame, text="✅ New Large Button:", font=('Segoe UI', 10, 'bold'), 
             fg=COLORS['success'], bg=COLORS['card']).pack()
    
    # Action section with large button
    action_card = create_card_frame(main_container)
    action_card.pack(fill='x', pady=(0, 20))
    
    action_content = tk.Frame(action_card, bg=COLORS['card'])
    action_content.pack(fill='x', padx=40, pady=40)
    
    # Large button with shadow effect
    button_frame = tk.Frame(action_content, bg=COLORS['card'])
    button_frame.pack(pady=20)
    
    # Shadow
    shadow_btn = tk.Button(
        button_frame,
        text="📄 UPLOAD & ANALYZE ROOF REPORT",
        font=('Segoe UI', 16, 'bold'),
        bg=darken_color(COLORS['secondary'], 0.7),
        fg=darken_color(COLORS['secondary'], 0.7),
        relief='flat',
        borderwidth=0,
        padx=52,
        pady=27,
        width=40,
        height=4,
        state='disabled'
    )
    shadow_btn.pack()
    
    # Main button
    large_btn = tk.Button(
        button_frame,
        text="📄 UPLOAD & ANALYZE ROOF REPORT",
        command=test_button_clicked,
        font=('Segoe UI', 16, 'bold'),
        bg=COLORS['secondary'],
        fg='white',
        relief='flat',
        borderwidth=0,
        cursor='hand2',
        padx=50,
        pady=25,
        width=40,
        height=4
    )
    large_btn.place(in_=shadow_btn, x=-2, y=-2)
    
    # Hover effects
    def on_enter(_):
        large_btn.config(
            bg=darken_color(COLORS['secondary']),
            font=('Segoe UI', 17, 'bold'),
            relief='raised',
            borderwidth=2
        )
    def on_leave(_):
        large_btn.config(
            bg=COLORS['secondary'],
            font=('Segoe UI', 16, 'bold'),
            relief='flat',
            borderwidth=0
        )
    
    large_btn.bind("<Enter>", on_enter)
    large_btn.bind("<Leave>", on_leave)
    
    # Help text
    tk.Label(
        action_content,
        text="💡 Much more prominent and user-friendly for contractors!",
        font=('Segoe UI', 11, 'italic'),
        fg=COLORS['warning'],
        bg=COLORS['card']
    ).pack(pady=(15, 0))
    
    # Benefits
    benefits_frame = tk.Frame(action_content, bg=COLORS['card'])
    benefits_frame.pack(pady=(20, 0))
    
    tk.Label(
        benefits_frame,
        text="✅ Improvements:",
        font=('Segoe UI', 12, 'bold'),
        fg=COLORS['text_primary'],
        bg=COLORS['card']
    ).pack()
    
    benefits = [
        "• 3x larger button size",
        "• Bold, clear text",
        "• Professional hover effects",
        "• Shadow depth effect",
        "• Better visual hierarchy",
        "• More accessible for all users"
    ]
    
    for benefit in benefits:
        tk.Label(
            benefits_frame,
            text=benefit,
            font=('Segoe UI', 10),
            fg=COLORS['text_secondary'],
            bg=COLORS['card']
        ).pack(anchor='w')
    
    root.mainloop()

if __name__ == "__main__":
    print("🏠 Isaiah Industries - UI Improvement Test")
    print("Testing the new large, prominent button design")
    print("Click the large button to test functionality!")
    show_ui_comparison()
