import pdfplumber
import tkinter as tk
from tkinter import filedialog, messagebox, simpledialog, ttk
from fpdf import FPDF
import pandas as pd
import re
import math
import os
from typing import List, Dict, Any, Optional
import threading
# OCR dependencies
from pdf2image import convert_from_path
import pytesseract

CSV_PATH = "parts.csv"
PRODUCTS = ["Country Manor Shake", "Oxford Shingle"]

def load_parts(product_name: str, csv_path: str = CSV_PATH) -> List[Dict[str, Any]]:
    """Load parts for a given product from CSV."""
    try:
        df = pd.read_csv(csv_path)
        if "product" not in df.columns:
            raise ValueError("CSV missing 'product' column.")
        return df[df["product"].str.lower() == product_name.lower()].to_dict(orient="records")
    except Exception as e:
        messagebox.showerror("CSV Error", f"Failed to load parts: {e}")
        return []

# --- UPDATED FUNCTION WITH OCR FALLBACK ---
def extract_roof_data(path: str) -> Dict[str, Any]:
    """Detect vendor and extract roof data from PDF, using OCR fallback if needed."""
    text_all = ""
    try:
        with pdfplumber.open(path) as pdf:
            text_all = "".join([page.extract_text() or "" for page in pdf.pages])
    except Exception as e:
        messagebox.showerror("PDF Error", f"Failed to read PDF: {e}")
        return {}

    # If no text found, try OCR
    if not text_all.strip():
        try:
            images = convert_from_path(path)
            ocr_text = ""
            for img in images:
                ocr_text += pytesseract.image_to_string(img)
            text_all = ocr_text
        except Exception as e:
            messagebox.showerror("OCR Error", f"Failed to extract text with OCR: {e}")
            return {}

    vendor = "unknown"
    if "EagleView" in text_all: vendor = "EagleView"
    elif "RoofSnap" in text_all: vendor = "RoofSnap"
    elif "Hover" in text_all: vendor = "Hover"
    elif "Roofr" in text_all or "Roof Sketch" in text_all: vendor = "Roofr"

    patterns = {
        "total_sqft": [r"Total\s+Area[:=]?\s*([\d,]+)", r"Roof Area[:=]?\s*([\d,]+)"],
        "ridge_length": [r"Ridges?[:=]?\s*([\d,]+)\s*ft", r"Ridge\s+Length[:=]?\s*([\d,]+)"],
        "hip_length": [r"Hips?[:=]?\s*([\d,]+)\s*ft"],
        "valley_length": [r"Valleys?[:=]?\s*([\d,]+)\s*ft"],
        "eaves_length": [r"Eaves?[:=]?\s*([\d,]+)\s*ft"],
    }

    result = {"vendor": vendor}
    for field, pats in patterns.items():
        for pat in pats:
            match = re.search(pat, text_all, re.IGNORECASE)
            if match:
                result[field] = int(match.group(1).replace(",", ""))
                break
        if field not in result:
            result[field] = None
    return result

# ... rest of the code remains unchanged ... 