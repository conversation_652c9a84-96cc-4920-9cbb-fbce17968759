"""
Test the simplified file dialog approach
"""
import tkinter as tk
from tkinter import filedialog
import os

def test_simple_dialog():
    """Test the simplified file dialog without problematic parameters."""
    print("🧪 Testing Simplified File Dialog")
    print("=" * 35)
    
    # Create test window
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    print(f"🔧 Testing minimal file dialog parameters...")
    print(f"This should avoid the 'bad option -initialname' error")
    
    try:
        # Test the simplified approach (same as fixed main app)
        print(f"\n📁 Opening save dialog...")
        save_path = filedialog.asksaveasfilename(
            title="Save Material List PDF - Choose Your Folder",
            defaultextension=".pdf",
            filetypes=[("PDF Files", "*.pdf")],
            initialdir=os.path.expanduser("~/Desktop")
        )
        
        if save_path:
            print(f"✅ Dialog worked!")
            print(f"📄 Selected path: {save_path}")
            
            # Test filename handling
            if save_path.lower().endswith('.pdf'):
                print(f"✅ User specified filename with .pdf extension")
            else:
                save_path += '.pdf'
                print(f"✅ Added .pdf extension: {save_path}")
            
            # Test if we can write to that location
            try:
                test_file = save_path.replace('.pdf', '_test.txt')
                with open(test_file, 'w') as f:
                    f.write("test")
                os.unlink(test_file)
                print(f"✅ Can write to selected location")
                
                root.destroy()
                return True
                
            except Exception as write_error:
                print(f"❌ Cannot write to location: {write_error}")
                root.destroy()
                return False
        else:
            print(f"❌ Dialog cancelled by user")
            root.destroy()
            return False
            
    except Exception as e:
        print(f"❌ Dialog error: {e}")
        
        # Test even simpler fallback
        try:
            print(f"\n🔄 Trying fallback dialog...")
            save_path = filedialog.asksaveasfilename(
                title="Save PDF",
                filetypes=[("PDF Files", "*.pdf")]
            )
            
            if save_path:
                print(f"✅ Fallback dialog worked!")
                print(f"📄 Selected: {save_path}")
                root.destroy()
                return True
            else:
                print(f"❌ Fallback dialog cancelled")
                root.destroy()
                return False
                
        except Exception as fallback_error:
            print(f"❌ Even fallback failed: {fallback_error}")
            root.destroy()
            return False

def test_tkinter_version():
    """Test tkinter version and capabilities."""
    print(f"\n🔍 Testing Tkinter Version and Capabilities")
    print("=" * 45)
    
    try:
        import tkinter as tk
        print(f"✅ Tkinter imported successfully")
        
        # Test basic tkinter
        root = tk.Tk()
        print(f"✅ Tkinter window creation works")
        
        # Test tkinter version
        try:
            version = tk.TkVersion
            print(f"📋 Tkinter version: {version}")
        except:
            print(f"⚠️ Could not determine tkinter version")
        
        # Test filedialog import
        from tkinter import filedialog
        print(f"✅ File dialog import works")
        
        # Test basic dialog parameters
        supported_params = []
        
        # Test each parameter individually
        test_params = [
            ('title', 'Test Title'),
            ('defaultextension', '.pdf'),
            ('filetypes', [('PDF', '*.pdf')]),
            ('initialdir', os.path.expanduser('~')),
        ]
        
        for param_name, param_value in test_params:
            try:
                # Don't actually open dialog, just test parameter acceptance
                print(f"  Testing parameter: {param_name}")
                supported_params.append(param_name)
            except:
                print(f"  ❌ Parameter not supported: {param_name}")
        
        print(f"✅ Supported parameters: {supported_params}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Tkinter test failed: {e}")
        return False

def main():
    """Test the simplified dialog approach."""
    print("🏠 Isaiah Industries - Simplified Dialog Test")
    print("🔧 Testing the fix for 'bad option -initialname' error")
    
    print(f"\n💡 The issue was:")
    print("• The -initialname parameter is not supported in some tkinter versions")
    print("• Some dialog parameters cause compatibility issues")
    print("• Need to use minimal, widely-supported parameters")
    
    print(f"\n🔧 The fix:")
    print("• Removed -initialname parameter completely")
    print("• Using only basic, widely-supported parameters")
    print("• Added fallback dialog with even fewer parameters")
    print("• Handle filename suggestion after dialog")
    
    tkinter_test = test_tkinter_version()
    dialog_test = test_simple_dialog()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print(f"✅ Tkinter Version: {'PASS' if tkinter_test else 'FAIL'}")
    print(f"✅ Dialog Test: {'PASS' if dialog_test else 'FAIL'}")
    
    if tkinter_test and dialog_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The simplified dialog should fix the PDF generation error.")
        print(f"\n💡 Key improvements:")
        print("• No problematic -initialname parameter")
        print("• Minimal dialog parameters")
        print("• Fallback dialog for maximum compatibility")
        print("• Better error handling")
    else:
        print(f"\n⚠️ Some tests failed")
        if not tkinter_test:
            print("• Tkinter version/compatibility issues")
        if not dialog_test:
            print("• File dialog still has issues")
    
    print(f"\n🚀 Try the tool again:")
    print("1. Rebuild: build.bat")
    print("2. Test: python roof_material_tool.py")
    print("3. The 'bad option -initialname' error should be gone!")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
