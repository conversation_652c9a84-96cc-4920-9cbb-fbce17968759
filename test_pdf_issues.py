"""
Test script to help diagnose PDF and manual input issues
"""
import tkinter as tk
from tkinter import filedialog, messagebox
import os

def test_manual_input():
    """Test the manual input functionality."""
    print("🧪 Testing Manual Input Functionality")
    print("=" * 50)
    
    # Simulate manual input data
    test_data = {
        "vendor": "manual_input",
        "extracted_text_length": 0,
        "total_sqft": 2000,
        "ridge_length": 120,
        "hip_length": 45,
        "valley_length": 50,
        "eaves_length": 180
    }
    
    print(f"📋 Test data: {test_data}")
    
    # Test parts loading
    try:
        import sys
        sys.path.append('.')
        from roof_material_tool import load_parts, calculate_materials
        
        print(f"\n🔍 Testing parts loading...")
        parts = load_parts("Country Manor Shake")
        
        if parts:
            print(f"✅ Found {len(parts)} parts")
            
            print(f"\n🧮 Testing material calculations...")
            results = calculate_materials(test_data, parts)
            
            if results:
                print(f"✅ Calculated {len(results)} material items:")
                for result in results[:5]:  # Show first 5
                    print(f"  • {result['part_number']}: {result['description']} - Qty: {result['count']}")
                
                # Test PDF generation
                print(f"\n📄 Testing PDF generation...")
                test_pdf_generation(test_data, results, "Country Manor Shake")
            else:
                print(f"❌ No materials calculated")
        else:
            print(f"❌ No parts found")
            
    except Exception as e:
        print(f"❌ Error in manual input test: {e}")

def test_pdf_generation(data, results, product):
    """Test PDF generation with sample data."""
    try:
        from roof_material_tool import MaterialListPDF
        import pandas as pd
        
        # Create a test PDF
        test_filename = "test_material_list.pdf"
        
        pdf = MaterialListPDF()
        pdf.add_page()
        pdf.set_font("Arial", "", 10)
        
        # Add header information
        pdf.cell(0, 10, f"Vendor Detected: {data.get('vendor', 'unknown')}", ln=1)
        pdf.cell(0, 10, f"Product Line: {product}", ln=1)
        pdf.cell(0, 10, f"Analysis Date: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M')}", ln=1)
        pdf.cell(0, 10, f"Text Extracted: {data.get('extracted_text_length', 0)} characters", ln=1)
        pdf.ln(5)
        
        # Add the materials table
        if results and len(results) > 0:
            pdf.add_table(results)
        else:
            pdf.cell(0, 10, "No materials calculated", ln=1)
        
        # Save the PDF
        pdf.output(test_filename)
        
        print(f"✅ PDF generated successfully: {test_filename}")
        
        # Check if file was created
        if os.path.exists(test_filename):
            file_size = os.path.getsize(test_filename)
            print(f"  • File size: {file_size} bytes")
            print(f"  • File location: {os.path.abspath(test_filename)}")
        else:
            print(f"❌ PDF file was not created")
            
    except Exception as e:
        print(f"❌ PDF generation failed: {e}")
        print(f"Error type: {type(e).__name__}")

def test_image_pdf_handling():
    """Test how the tool handles image-based PDFs."""
    print(f"\n🖼️ Testing Image-Based PDF Handling")
    print("=" * 50)
    
    print(f"📋 For image-based or scanned PDFs, here are solutions:")
    print(f"")
    print(f"✅ Best Solutions:")
    print(f"  1. Export from your roof software as 'PDF with text'")
    print(f"  2. Use 'Print to PDF' instead of 'Save as Image'")
    print(f"  3. Check if your software has 'Export Data' or 'Export Report' options")
    print(f"")
    print(f"🔧 Software-Specific Tips:")
    print(f"  • EagleView: Use 'Export Report' not 'Save Image'")
    print(f"  • RoofSnap: Export as 'PDF Report' not screenshot")
    print(f"  • Hover: Use 'Generate Report' feature")
    print(f"  • GAF QuickMeasure: Export full report")
    print(f"")
    print(f"⚠️ If you only have image-based PDFs:")
    print(f"  • Use manual input (it's faster than you think!)")
    print(f"  • The tool will guide you through entering measurements")
    print(f"  • You only need: Total sqft, Ridge, Hip, Valley, Eaves lengths")

def test_file_dialog():
    """Test file dialog functionality."""
    print(f"\n📁 Testing File Dialog...")
    
    root = tk.Tk()
    root.withdraw()  # Hide main window
    
    try:
        test_path = filedialog.asksaveasfilename(
            title="Test Save Dialog",
            defaultextension=".pdf",
            filetypes=[("PDF", "*.pdf")],
            initialname="Test_Material_List"
        )
        
        if test_path:
            print(f"✅ File dialog works: {test_path}")
            
            # Test if path is valid
            if test_path.strip() and len(test_path) > 0:
                print(f"✅ Path is valid")
                
                # Ensure .pdf extension
                if not test_path.lower().endswith('.pdf'):
                    test_path += '.pdf'
                    print(f"✅ Added .pdf extension: {test_path}")
                
            else:
                print(f"❌ Invalid path")
        else:
            print(f"❌ No file selected")
            
    except Exception as e:
        print(f"❌ File dialog error: {e}")
    
    root.destroy()

def main():
    """Run all tests."""
    print("🏠 Isaiah Industries - PDF & Manual Input Issue Diagnosis")
    print("🔧 Testing common issues and solutions")
    
    test_manual_input()
    test_image_pdf_handling()
    test_file_dialog()
    
    print(f"\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print("✅ Manual input functionality tested")
    print("✅ PDF generation tested")
    print("✅ Image-based PDF solutions provided")
    print("✅ File dialog tested")
    
    print(f"\n💡 Quick Solutions:")
    print("1. For image PDFs: Use manual input or get text-based PDF")
    print("2. For PDF errors: Check file path and permissions")
    print("3. For parts errors: Ensure parts.csv is in same folder")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
