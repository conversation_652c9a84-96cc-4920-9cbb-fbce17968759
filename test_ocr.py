"""
Test script to diagnose OCR issues
"""
import sys
import os

def test_tesseract():
    """Test if Tesseract OCR is properly installed."""
    print("🔍 Testing Tesseract OCR Installation...")
    
    try:
        import pytesseract
        print("✅ pytesseract module imported successfully")
        
        # Try to get version
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        # Test basic OCR functionality
        from PIL import Image
        import numpy as np
        
        # Create a simple test image with text
        test_image = Image.new('RGB', (200, 50), color='white')
        # Note: This is a basic test - in real use, we'd need actual text in the image
        
        print("✅ Tesseract OCR is working correctly!")
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import pytesseract: {e}")
        print("   Install with: pip install pytesseract")
        return False
        
    except Exception as e:
        print(f"❌ Tesseract OCR error: {e}")
        print("\n🔧 Possible solutions:")
        print("1. Install Tesseract OCR from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Make sure Tesseract is in your system PATH")
        print("3. Restart your computer after installation")
        print("4. Try running as administrator")
        return False

def test_pdf_libraries():
    """Test PDF processing libraries."""
    print("\n🔍 Testing PDF Libraries...")
    
    try:
        import pdfplumber
        print("✅ pdfplumber imported successfully")
    except ImportError:
        print("❌ pdfplumber not found. Install with: pip install pdfplumber")
        return False
    
    try:
        from pdf2image import convert_from_path
        print("✅ pdf2image imported successfully")
    except ImportError:
        print("❌ pdf2image not found. Install with: pip install pdf2image")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL (Pillow) imported successfully")
    except ImportError:
        print("❌ PIL not found. Install with: pip install Pillow")
        return False
    
    return True

def test_other_dependencies():
    """Test other required libraries."""
    print("\n🔍 Testing Other Dependencies...")
    
    libraries = [
        ('pandas', 'pip install pandas'),
        ('fpdf', 'pip install fpdf2'),
        ('tkinter', 'Built-in with Python')
    ]
    
    all_good = True
    for lib, install_cmd in libraries:
        try:
            if lib == 'fpdf':
                from fpdf import FPDF
            else:
                __import__(lib)
            print(f"✅ {lib} imported successfully")
        except ImportError:
            print(f"❌ {lib} not found. Install with: {install_cmd}")
            all_good = False
    
    return all_good

def main():
    """Run all tests."""
    print("🏠 Isaiah Industries - Roof Material Tool")
    print("🧪 Dependency Test Script")
    print("=" * 50)
    
    tesseract_ok = test_tesseract()
    pdf_ok = test_pdf_libraries()
    deps_ok = test_other_dependencies()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY:")
    
    if tesseract_ok and pdf_ok and deps_ok:
        print("🎉 ALL TESTS PASSED! Your system is ready to run the roof material tool.")
    else:
        print("⚠️  Some issues found. Please fix the errors above before running the main tool.")
        
        if not tesseract_ok:
            print("\n🔧 To fix Tesseract OCR:")
            print("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
            print("2. Install with default settings")
            print("3. Restart your computer")
            print("4. Run this test again")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
