"""
Test the PDF generation fix
"""
import sys
import os
import tempfile

def test_pdf_generation():
    """Test PDF generation with the fixed code."""
    print("🧪 Testing PDF Generation Fix")
    print("=" * 50)
    
    try:
        sys.path.append('.')
        from roof_material_tool import MaterialListPDF, calculate_materials
        import pandas as pd
        
        print("✅ Imports successful")
        
        # Create sample data
        sample_data = {
            "vendor": "test",
            "extracted_text_length": 100,
            "total_sqft": 2000,
            "ridge_length": 120,
            "hip_length": 45,
            "valley_length": 50,
            "eaves_length": 180
        }
        
        sample_parts = [
            {"part_number": "TEST-001", "description": "Test Panel", "source_metric": "total_sqft", "coverage": 100, "waste": 0.10, "unit": "sq ft"},
            {"part_number": "TEST-002", "description": "Test Ridge", "source_metric": "ridge_length", "coverage": 10, "waste": 0.05, "unit": "linear ft"},
        ]
        
        print("📋 Sample data created")
        
        # Test material calculation
        print("🧮 Testing material calculations...")
        results = calculate_materials(sample_data, sample_parts)
        
        if results:
            print(f"✅ Calculated {len(results)} material items")
        else:
            print("❌ No materials calculated")
            return False
        
        # Test PDF creation with multiple methods
        print("\n📄 Testing PDF creation methods...")
        
        # Method 1: Simple output
        try:
            print("  Testing Method 1: Simple output...")
            pdf1 = MaterialListPDF()
            pdf1.add_page()
            pdf1.set_font("Arial", "", 10)
            pdf1.cell(0, 10, "Test PDF - Method 1", ln=1)
            pdf1.add_table(results)
            
            temp_path1 = "test_pdf_method1.pdf"
            pdf1.output(temp_path1, 'F')
            
            if os.path.exists(temp_path1) and os.path.getsize(temp_path1) > 1000:
                print("  ✅ Method 1 successful")
                os.unlink(temp_path1)
                method1_works = True
            else:
                print("  ❌ Method 1 failed")
                method1_works = False
        except Exception as e:
            print(f"  ❌ Method 1 error: {e}")
            method1_works = False
        
        # Method 2: No parameters
        try:
            print("  Testing Method 2: No parameters...")
            pdf2 = MaterialListPDF()
            pdf2.add_page()
            pdf2.set_font("Arial", "", 10)
            pdf2.cell(0, 10, "Test PDF - Method 2", ln=1)
            pdf2.add_table(results)
            
            temp_path2 = "test_pdf_method2.pdf"
            pdf2.output(temp_path2)
            
            if os.path.exists(temp_path2) and os.path.getsize(temp_path2) > 1000:
                print("  ✅ Method 2 successful")
                os.unlink(temp_path2)
                method2_works = True
            else:
                print("  ❌ Method 2 failed")
                method2_works = False
        except Exception as e:
            print(f"  ❌ Method 2 error: {e}")
            method2_works = False
        
        # Method 3: String output
        try:
            print("  Testing Method 3: String output...")
            pdf3 = MaterialListPDF()
            pdf3.add_page()
            pdf3.set_font("Arial", "", 10)
            pdf3.cell(0, 10, "Test PDF - Method 3", ln=1)
            pdf3.add_table(results)
            
            temp_path3 = "test_pdf_method3.pdf"
            pdf_string = pdf3.output(dest='S')
            
            with open(temp_path3, 'wb') as f:
                if isinstance(pdf_string, str):
                    f.write(pdf_string.encode('latin1'))
                else:
                    f.write(pdf_string)
            
            if os.path.exists(temp_path3) and os.path.getsize(temp_path3) > 1000:
                print("  ✅ Method 3 successful")
                os.unlink(temp_path3)
                method3_works = True
            else:
                print("  ❌ Method 3 failed")
                method3_works = False
        except Exception as e:
            print(f"  ❌ Method 3 error: {e}")
            method3_works = False
        
        # Summary
        working_methods = sum([method1_works, method2_works, method3_works])
        print(f"\n📊 Results: {working_methods}/3 methods working")
        
        if working_methods > 0:
            print("✅ At least one PDF generation method works!")
            print("The fixed code should handle PDF generation properly.")
            return True
        else:
            print("❌ All PDF generation methods failed!")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_gui_dialogs():
    """Test GUI dialog functionality."""
    print(f"\n🖥️ Testing GUI Dialogs")
    print("=" * 30)
    
    try:
        import tkinter as tk
        from tkinter import filedialog
        
        print("📱 Testing basic tkinter functionality...")
        
        # Test basic window creation
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        print("✅ Tkinter window creation works")
        
        # Test that we can import filedialog
        print("✅ File dialog import works")
        
        root.destroy()
        
        print("✅ GUI components test passed")
        return True
        
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🏠 Isaiah Industries - PDF Generation Fix Test")
    print("🔧 Testing the fixes for PDF generation errors")
    
    pdf_test = test_pdf_generation()
    gui_test = test_gui_dialogs()
    
    print(f"\n" + "=" * 50)
    print("🎯 TEST RESULTS:")
    print(f"✅ PDF Generation: {'PASS' if pdf_test else 'FAIL'}")
    print(f"✅ GUI Dialogs: {'PASS' if gui_test else 'FAIL'}")
    
    if pdf_test and gui_test:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The PDF generation fix should work.")
        print(f"\n💡 The fix includes:")
        print("• Better error handling for file dialogs")
        print("• Multiple PDF output methods as fallbacks")
        print("• GUI update calls to prevent tkinter issues")
        print("• Safer file path handling")
    else:
        print(f"\n⚠️ Some tests failed.")
        if not pdf_test:
            print("• PDF generation still has issues")
        if not gui_test:
            print("• GUI dialog issues detected")
    
    print(f"\n🚀 Try running the tool now:")
    print("python roof_material_tool.py")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
