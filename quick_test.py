"""
Quick test to check parts loading
"""
import pandas as pd
import os

def test_parts():
    print("🔍 Testing Parts Database...")
    
    # Check if parts.csv exists
    if not os.path.exists("parts.csv"):
        print("❌ parts.csv not found!")
        print(f"Current directory: {os.getcwd()}")
        print("Files in directory:")
        for file in os.listdir("."):
            print(f"  • {file}")
        return
    
    # Load parts
    try:
        df = pd.read_csv("parts.csv")
        print(f"✅ Loaded parts.csv: {len(df)} rows")
        print(f"Columns: {list(df.columns)}")
        
        # Test product filtering
        products = ["Country Manor Shake", "Oxford Shingle"]
        
        for product in products:
            parts = df[df["product"].str.lower() == product.lower()]
            print(f"✅ {product}: {len(parts)} parts found")
            
            if len(parts) > 0:
                print(f"  Sample parts:")
                for i, row in parts.head(3).iterrows():
                    print(f"    • {row['part_number']}: {row['description']}")
            else:
                print(f"  ❌ No parts found for {product}")
        
    except Exception as e:
        print(f"❌ Error loading parts: {e}")

def test_manual_data():
    print("\n🔍 Testing Manual Data Processing...")
    
    # Simulate manual input data
    test_data = {
        "vendor": "manual_input",
        "extracted_text_length": 0,
        "total_sqft": 2000,
        "ridge_length": 120,
        "hip_length": 0,
        "valley_length": 50,
        "eaves_length": 180
    }
    
    print(f"Test data: {test_data}")
    
    # Test parts loading
    try:
        df = pd.read_csv("parts.csv")
        product = "Country Manor Shake"
        parts = df[df["product"].str.lower() == product.lower()].to_dict(orient="records")
        
        print(f"✅ Found {len(parts)} parts for {product}")
        
        # Test calculation
        import math
        results = []
        for row in parts:
            src_val = test_data.get(row.get("source_metric", ""), 0)
            coverage = float(row.get("coverage", 1))
            waste = float(row.get("waste", 0))
            count = math.ceil((src_val / coverage) * (1 + waste)) if coverage else 0
            
            results.append({
                "part_number": row.get("part_number", ""),
                "description": row.get("description", ""),
                "count": count
            })
        
        print(f"✅ Calculated materials:")
        for result in results[:5]:  # Show first 5
            print(f"  • {result['part_number']}: {result['description']} - Qty: {result['count']}")
            
    except Exception as e:
        print(f"❌ Error in calculation: {e}")

if __name__ == "__main__":
    test_parts()
    test_manual_data()
    input("\nPress Enter to exit...")
