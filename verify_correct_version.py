"""
Verify you're running the correct version without OCR dependencies
"""
import sys
import os

def check_version():
    """Check which version of the tool is being used."""
    print("🔍 Checking Roof Material Tool Version")
    print("=" * 50)
    
    # Check current directory
    current_dir = os.getcwd()
    print(f"📁 Current directory: {current_dir}")
    
    # Check for main file
    main_file = "roof_material_tool.py"
    if os.path.exists(main_file):
        print(f"✅ Found {main_file}")
        
        # Check the imports in the file
        with open(main_file, 'r') as f:
            content = f.read()
        
        # Check for problematic imports
        has_ocr = "pytesseract" in content or "pdf2image" in content
        has_complex_extraction = "advanced_pdf_extraction" in content
        
        print(f"\n📋 Version Analysis:")
        print(f"  • Has OCR dependencies: {'❌ YES (BAD)' if has_ocr else '✅ NO (GOOD)'}")
        print(f"  • Has complex extraction: {'⚠️ YES' if has_complex_extraction else '✅ NO (GOOD)'}")
        
        # Check for key functions
        has_extract_roof_data = "def extract_roof_data" in content
        has_analyze_text = "def analyze_text_for_measurements" in content
        has_calculate_materials = "def calculate_materials" in content
        has_pdf_class = "class MaterialListPDF" in content
        
        print(f"\n🔧 Required Functions:")
        print(f"  • extract_roof_data: {'✅' if has_extract_roof_data else '❌'}")
        print(f"  • analyze_text_for_measurements: {'✅' if has_analyze_text else '❌'}")
        print(f"  • calculate_materials: {'✅' if has_calculate_materials else '❌'}")
        print(f"  • MaterialListPDF: {'✅' if has_pdf_class else '❌'}")
        
        # Check file size (the fixed version should be smaller)
        file_size = os.path.getsize(main_file)
        print(f"\n📊 File size: {file_size:,} bytes")
        
        if has_ocr:
            print(f"\n❌ WRONG VERSION DETECTED!")
            print(f"You're running an old version with OCR dependencies.")
            print(f"This will cause the errors you're seeing.")
            return False
        elif file_size > 100000:  # Very large file
            print(f"\n⚠️ COMPLEX VERSION DETECTED!")
            print(f"You might be running the complex version that causes issues.")
            return False
        else:
            print(f"\n✅ CORRECT VERSION DETECTED!")
            print(f"You're running the fixed, simplified version.")
            return True
    else:
        print(f"❌ {main_file} not found!")
        return False

def test_imports():
    """Test that the imports work correctly."""
    print(f"\n🧪 Testing Imports")
    print("=" * 30)
    
    try:
        # Test basic imports first
        import pdfplumber
        print("✅ pdfplumber")
        
        import tkinter as tk
        print("✅ tkinter")
        
        from fpdf import FPDF
        print("✅ fpdf")
        
        import pandas as pd
        print("✅ pandas")
        
        # Test main module import
        sys.path.append('.')
        from roof_material_tool import extract_roof_data, analyze_text_for_measurements
        print("✅ Main functions imported")
        
        print(f"\n✅ ALL IMPORTS SUCCESSFUL!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        if "pytesseract" in str(e) or "pdf2image" in str(e):
            print(f"🔍 This confirms you're running the old version with OCR!")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    """Main verification function."""
    print("🏠 Isaiah Industries - Version Verification")
    print("🔧 Checking if you're running the correct version")
    
    version_ok = check_version()
    imports_ok = test_imports()
    
    print(f"\n" + "=" * 50)
    print("🎯 VERIFICATION RESULTS:")
    print(f"✅ Correct Version: {'PASS' if version_ok else 'FAIL'}")
    print(f"✅ Imports Working: {'PASS' if imports_ok else 'FAIL'}")
    
    if version_ok and imports_ok:
        print(f"\n🎉 SUCCESS!")
        print(f"You're running the correct, fixed version.")
        print(f"\n💡 Next steps:")
        print("1. Run: python roof_material_tool.py")
        print("2. Test with your PDF files")
        print("3. Should work without OCR errors!")
    else:
        print(f"\n❌ ISSUES DETECTED!")
        if not version_ok:
            print("• You're running the wrong version of the tool")
        if not imports_ok:
            print("• Import errors detected")
        
        print(f"\n🔧 To fix:")
        print("1. Make sure you're in the correct directory")
        print("2. Run from the main folder, not the test folder")
        print("3. The correct file should NOT have pytesseract imports")

if __name__ == "__main__":
    main()
    input("\nPress Enter to exit...")
